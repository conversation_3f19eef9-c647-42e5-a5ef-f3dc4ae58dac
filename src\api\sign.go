package api

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
)

// GenerateSign generates a signature for the API request.
// The actual signing algorithm might differ, this is a common implementation.
func GenerateSign(secretKey, timestamp string) string {
	data := fmt.Sprintf("secretKey=%s&timestamp=%s", secretKey, timestamp)
	hasher := md5.New()
	hasher.Write([]byte(data))
	return hex.EncodeToString(hasher.Sum(nil))
}