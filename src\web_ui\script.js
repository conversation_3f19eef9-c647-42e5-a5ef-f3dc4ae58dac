class ChatApp {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.clearBtn = document.getElementById('clearBtn');
        this.charCount = document.getElementById('charCount');
        
        this.initEventListeners();
        this.updateCharCount();
    }
    
    initEventListeners() {
        // 发送按钮点击事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 清空按钮点击事件
        this.clearBtn.addEventListener('click', () => this.clearChat());
        
        // 输入框事件
        this.messageInput.addEventListener('input', () => this.updateCharCount());
        this.messageInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
        
        // 自动调整输入框高度
        this.messageInput.addEventListener('input', () => this.autoResize());
    }
    
    handleKeyDown(e) {
        // Ctrl+Enter 发送消息
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            this.sendMessage();
        }
        // Enter 换行（默认行为）
    }
    
    updateCharCount() {
        const length = this.messageInput.value.length;
        this.charCount.textContent = `${length}/2000`;
        
        // 字符数接近限制时改变颜色
        if (length > 1800) {
            this.charCount.style.color = '#f44336';
        } else if (length > 1500) {
            this.charCount.style.color = '#ff9800';
        } else {
            this.charCount.style.color = '#666';
        }
    }
    
    autoResize() {
        this.messageInput.style.height = 'auto';
        const maxHeight = 120; // 最大高度
        const newHeight = Math.min(this.messageInput.scrollHeight, maxHeight);
        this.messageInput.style.height = newHeight + 'px';
    }
    
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;
        
        // 禁用发送按钮
        this.sendBtn.disabled = true;
        
        // 添加用户消息
        this.addMessage(message, 'user');
        
        // 清空输入框
        this.messageInput.value = '';
        this.updateCharCount();
        this.autoResize();
        
        // 显示AI正在输入的指示器
        const typingId = this.showTypingIndicator();
        
        try {
            // 模拟AI响应（这里可以替换为实际的AI API调用）
            const response = await this.getAIResponse(message);
            
            // 移除输入指示器
            this.removeTypingIndicator(typingId);
            
            // 添加AI响应
            this.addMessage(response, 'ai');
            
        } catch (error) {
            // 移除输入指示器
            this.removeTypingIndicator(typingId);
            
            // 显示错误消息
            this.addMessage('抱歉，我现在无法回应。请稍后再试。', 'ai');
            console.error('AI响应错误:', error);
        }
        
        // 重新启用发送按钮
        this.sendBtn.disabled = false;
        
        // 聚焦输入框
        this.messageInput.focus();
    }
    
    addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        // 处理换行
        const formattedContent = content.replace(/\n/g, '<br>');
        contentDiv.innerHTML = `<p>${formattedContent}</p>`;
        
        messageDiv.appendChild(contentDiv);
        this.chatMessages.appendChild(messageDiv);
        
        // 滚动到底部
        this.scrollToBottom();
    }
    
    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message';
        typingDiv.id = 'typing-indicator';
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'typing-indicator';
        contentDiv.innerHTML = `
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        `;
        
        typingDiv.appendChild(contentDiv);
        this.chatMessages.appendChild(typingDiv);
        
        this.scrollToBottom();
        return 'typing-indicator';
    }
    
    removeTypingIndicator(id) {
        const indicator = document.getElementById(id);
        if (indicator) {
            indicator.remove();
        }
    }
    
    async getAIResponse(message) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
        
        // 这里是模拟的AI响应逻辑
        // 在实际应用中，这里应该调用真实的AI API
        const responses = [
            "我理解您的问题。让我为您详细解答...",
            "这是一个很好的问题！根据我的理解...",
            "感谢您的提问。我建议您可以考虑以下几个方面...",
            "基于您提供的信息，我认为...",
            "这个问题涉及多个方面，让我逐一为您分析...",
        ];
        
        // 根据用户输入生成更相关的响应
        if (message.includes('你好') || message.includes('您好')) {
            return "您好！很高兴为您服务。有什么我可以帮助您的吗？";
        } else if (message.includes('谢谢') || message.includes('感谢')) {
            return "不客气！如果您还有其他问题，随时可以问我。";
        } else if (message.includes('再见') || message.includes('拜拜')) {
            return "再见！祝您工作顺利，有需要随时联系我。";
        } else {
            const randomResponse = responses[Math.floor(Math.random() * responses.length)];
            return `${randomResponse}\n\n针对您的问题"${message}"，我需要更多信息才能给出更准确的建议。您能提供更多详细信息吗？`;
        }
    }
    
    clearChat() {
        // 确认清空
        if (confirm('确定要清空所有对话记录吗？')) {
            // 保留系统欢迎消息
            this.chatMessages.innerHTML = `
                <div class="message system-message">
                    <div class="message-content">
                        <p>对话已清空。有什么可以帮助您的吗？</p>
                    </div>
                </div>
            `;
        }
    }
    
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }
}

// 页面加载完成后初始化聊天应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});

// 防止页面刷新时丢失焦点
window.addEventListener('load', () => {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.focus();
    }
});
