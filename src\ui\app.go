package ui

import (
	"fmt"
	"simple_inventory_management_system/api"
	"simple_inventory_management_system/internal/auth"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

const (
	AppTitle                       = "生活必需品流通保供监测"
	SystemName                     = "生活必需品流通保供数据上报系统"
	PageSize                       = 20
	ERROR_MSG_COLUMN_WIDTH float32 = 500
	DEFAULT_ROW_HEIGHT     float32 = 28
)

type App struct {
	fyneApp     fyne.App
	mainWin     fyne.Window
	user        *api.User
	refreshTabs map[string]func() // 存储各标签页的刷新函数
}

func NewApp() *App {
	fontPath := EnsureMicrosoftYaheiFont()

	a := app.NewWithID("")
	a.Settings().SetTheme(NewMyTheme(fontPath))

	return &App{
		fyneApp: a,
	}
}

func (a *App) Run() {
	logger.Init()
	err := database.InitDB()
	if err != nil {
		logger.Log.Fatal("数据库初始化失败！", err)
	}
	companyName, _ := database.GetConfig("companyName")
	if companyName != "" {
		a.mainWin = a.fyneApp.NewWindow(companyName)
	} else {
		a.mainWin = a.fyneApp.NewWindow(AppTitle)
	}
	if a.refreshTabs == nil {
		a.refreshTabs = make(map[string]func())
	}
	a.showLoginScreen()
	a.mainWin.ShowAndRun()
}

// handleLogin 已被内联到showLoginScreen中，保留此函数以兼容可能的外部调用
func (a *App) handleLogin(username, password string) {
	// 显示登录中对话框
	progress := dialog.NewProgress("登录", "正在验证用户信息...", a.mainWin)
	progress.Show()

	// 在goroutine中处理登录
	go func() {
		// 执行登录
		user, err := auth.AuthenticateUser(username, password)

		// 在主线程中更新UI
		go func() {
			// 关闭进度对话框
			progress.Hide()

			if err != nil {
				// 登录失败，显示错误信息
				dialog.ShowError(err, a.mainWin)
			} else {
				// 登录成功，切换到主界面
				a.user = user
				a.showMainScreen()
			}
		}()
	}()
}

func (a *App) showLoginScreen() {
	// 重置Canvas的键盘事件处理，避免多个事件处理器重叠
	a.mainWin.Canvas().SetOnTypedKey(nil)

	// 创建带有固定宽度的用户名输入框
	usernameEntry := newFixedWidthEntry(200)
	usernameEntry.SetPlaceHolder("用户名")

	// 创建密码输入框
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("密码")

	// 创建登录状态标签（初始隐藏）
	statusLabel := widget.NewLabel("")
	statusLabel.Hide()

	var form *widget.Form
	// 创建登录表单
	form = &widget.Form{
		Items: []*widget.FormItem{
			{Text: "用户名", Widget: usernameEntry},
			{Text: "密码", Widget: passwordEntry},
		},
		OnSubmit: func() {
			// 禁用表单，显示登录中状态
			form.Disable()
			statusLabel.SetText("登录中...")
			statusLabel.Show()

			// 在goroutine中处理登录，避免阻塞UI
			go func() {
				// 执行登录
				user, err := auth.AuthenticateUser(usernameEntry.Text, passwordEntry.Text)

				// 在主线程中更新UI
				if err != nil {
					// 登录失败，显示错误信息
					statusLabel.SetText(fmt.Sprintf("登录失败: %v", err))
					statusLabel.Show()
					form.Enable()
				} else {
					// 登录成功，切换到主界面
					a.user = user
					a.showMainScreen()
				}
			}()
		},
		SubmitText: "登录",
	}

	// 设置回车键提交表单
	setupReturnKeySubmit(a.mainWin.Canvas(), form.OnSubmit, usernameEntry, passwordEntry)

	// 创建登录界面布局
	content := container.NewCenter(
		container.NewVBox(
			widget.NewLabel(SystemName),
			form,
			statusLabel,
		),
	)

	// 设置窗口内容
	a.mainWin.SetContent(content)

	// 调整窗口大小并居中显示
	go func() {
		time.Sleep(50 * time.Millisecond)
		a.mainWin.Resize(fyne.NewSize(400, 200))
		a.mainWin.CenterOnScreen()
	}()
}

func setupReturnKeySubmit(canvas fyne.Canvas, f func(), entries ...fyne.Focusable) {
	canvas.SetOnTypedKey(func(ke *fyne.KeyEvent) {
		if ke.Name == fyne.KeyEnter {
			fmt.Println("enter")
			for _, entry := range entries {
				if entry == canvas.Focused() {
					f()
					return
				}
			}
		}
	})
}

func (a *App) showMainScreen() {
	// 重置Canvas的键盘事件处理，避免多个事件处理器重叠
	a.mainWin.Canvas().SetOnTypedKey(nil)

	// 显示加载中对话框
	progress := dialog.NewCustomWithoutButtons("加载中", container.NewVBox(
		widget.NewLabel("正在加载应用程序..."),
		widget.NewProgressBarInfinite(),
	), a.mainWin)
	progress.Show()

	// 在goroutine中创建UI组件，避免阻塞主线程
	go func() {
		// 创建标签页
		tabs := container.NewAppTabs(
			container.NewTabItem("数据上报", a.createSubmissionTab()),
			container.NewTabItem("上报历史", a.createHistoryTab()),
			container.NewTabItem("模板下载", a.createTemplatesTab()),
			container.NewTabItem("行政区划", a.createAdminDivisionsTab()),
			container.NewTabItem("地区代码", a.createRegionsTab()),
			container.NewTabItem("品类管理", a.createCategoriesTab()),
		)

		// 确保只有管理员才能看到和访问管理选项卡
		if a.user != nil && a.user.Role == "管理员" {
			tabs.Append(container.NewTabItem("配置管理", a.createConfigTab()))
			tabs.Append(container.NewTabItem("系统管理", a.createSystemManagementTab()))
		}

		// 创建登出按钮
		logoutButton := widget.NewButton("登出", func() {
			// 显示确认对话框
			dialog.ShowConfirm("确认登出", "确定要退出登录吗？", func(confirm bool) {
				if confirm {
					// 在登出前清理资源
					a.user = nil
					// 使用主线程调用showLoginScreen，避免并发问题
					a.showLoginScreen()
				}
			}, a.mainWin)
		})

		// 设置标签切换事件
		tabs.OnSelected = func(tab *container.TabItem) {
			if refreshFunc, ok := a.refreshTabs[tab.Text]; ok {
				refreshFunc()
			}
		}

		// 创建头部区域
		header := container.NewHBox(
			widget.NewLabel(fmt.Sprintf("欢迎, %s (%s)", a.user.Username, a.user.Role)),
			logoutButton,
		)

		// 创建主界面布局
		content := container.NewBorder(header, nil, nil, nil, tabs)

		// 在主线程中更新UI
		go func() {
			// 隐藏加载对话框
			progress.Hide()

			// 设置窗口内容
			a.mainWin.SetContent(content)

			// 调整窗口大小并居中显示
			a.mainWin.Resize(fyne.NewSize(1024, 768))
			a.mainWin.CenterOnScreen()
		}()
	}()
}

// func (a *App) safeResizeAndCenter(size fyne.Size) {
// 	// 在调整大小和居中之前添加一个小的延迟
// 	// 有时，特别是在Windows上，立即调整大小可能会导致窗口位置不正确
// 	// time.Sleep(50 * time.Millisecond)
// 	a.mainWin.Resize(size)
// 	a.mainWin.CenterOnScreen()
// }
