package ui

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"simple_inventory_management_system/api"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"simple_inventory_management_system/internal/models"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
	"github.com/xuri/excelize/v2"
)

var (
	interfaceDict     map[string]string
	initInterfaceDict sync.Once
	lastErrorFilePath string // 存储最近生成的错误文件路径
)

func GetInterfaceDict() map[string]string {
	initInterfaceDict.Do(func() {
		interfaceDict = map[string]string{
			"3.2":  "市场监测日度商超价格数据信息",
			"3.3":  "市场监测日度批发价格数据信息",
			"3.4":  "市场监测日度农贸价格数据信息",
			"3.5":  "应急保供日度商超进销存数据信息",
			"3.6":  "应急保供日度批发进销存数据信息",
			"3.7":  "食品加工企业月度数据信息",
			"3.8":  "物流运输信息监测数据",
			"3.9":  "终端配送信息监测数据",
			"3.10": "（网点维度）应急保供日度商超进销存数据信息",
			"3.11": "（网点维度）应急保供日度批发进销存数据信息",
		}
	})

	// 返回副本防止外部修改
	cp := make(map[string]string, len(interfaceDict))
	for k, v := range interfaceDict {
		cp[k] = v
	}
	return cp
}

func (a *App) createSubmissionTab() fyne.CanvasObject {
	// 预先加载配置和报告类型列表
	var reportTypeList []string

	// 获取报告标签配置
	labels, err := database.GetConfig("report_labels")
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("获取配置失败")
	}

	// 构建报告类型列表
	dict := GetInterfaceDict()
	if enhancedTrim(labels) != "" {
		// 如果有配置的标签，只显示相关的报告类型
		labelList := strings.Split(labels, ",")
		reportTypeList = make([]string, 0, len(labelList))
		for _, lx := range labelList {
			if name, ok := dict[lx]; ok {
				reportTypeList = append(reportTypeList, name)
			}
		}
	} else {
		// 如果没有配置，显示所有报告类型
		reportTypeList = make([]string, 0, len(dict))
		for _, v := range dict {
			reportTypeList = append(reportTypeList, v)
		}
	}

	reportTypeSelect := widget.NewSelect(reportTypeList, func(s string) {
		showAssociatedCategoriesInfo(a, s)
	})
	reportTypeSelect.PlaceHolder = "请选择上报类型"

	filePathEntry := newFixedWidthEntry(200) //widget.NewEntry()
	filePathEntry.SetPlaceHolder("请选择要上传的数据文件路径")

	fileSelectButton := widget.NewButton("选择文件", func() {
		dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil || reader == nil {
				return
			}
			filePathEntry.SetText(reader.URI().Path())
		}, a.mainWin)
	})

	resultLabel := widget.NewLabel("提交结果将显示在这里")
	resultLabel.Wrapping = fyne.TextWrapWord
	// 先声明按钮变量
	var submitButton *widget.Button
	submitButton = widget.NewButton("提交", func() {
		// 禁用提交按钮，防止重复提交
		submitButton.Disable()
		resultLabel.SetText("正在处理，请稍候...")

		// 在goroutine中处理提交，避免阻塞UI
		go func() {
			startTime := time.Now()
			reportType := reportTypeSelect.Selected
			filePath := filePathEntry.Text
			var errorMsg string
			var dstPath string

			// 在主线程中更新UI
			updateUI := func(msg string, isError bool) {
				go func() {
					resultLabel.SetText(msg)
					submitButton.Enable()
				}()
			}

			// 验证输入
			if reportType == "" || filePath == "" {
				updateUI("错误: 请选择上报类型并选择文件。", true)
				return
			}

			// 检查文件扩展名是否为Excel文件
			fileExt := strings.ToLower(filepath.Ext(filePath))
			if fileExt != ".xlsx" && fileExt != ".xls" {
				errorMsg = "错误: 请上传Excel文件(.xlsx或.xls格式)"
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, reportType, filePath, startTime, time.Now(), time.Now(), "failure", errorMsg)
				return
			}

			// 1. 保存文件到日期命名的文件夹
			today := time.Now().Format("2006-01-02")
			uploadDir := filepath.Join("uploads", today)
			if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
				errorMsg = fmt.Sprintf("创建目录失败: %v", err)
				updateUI(errorMsg, true)
				return
			}

			dstPath = filepath.Join(uploadDir, filepath.Base(filePath))
			if err := copyFile(filePath, dstPath); err != nil {
				errorMsg = fmt.Sprintf("保存文件失败: %v", err)
				updateUI(errorMsg, true)
				logger.Log.WithFields(logrus.Fields{
					"filePath": filePath,
					"dstPath":  dstPath,
					"error":    err,
					"user":     a.user.Username,
				}).Error("文件保存失败")
				return
			}

			// 2. 读取配置并初始化客户端
			baseURL, err1 := database.GetConfig("baseURL")
			secretId, err2 := database.GetConfig("secretId")
			secretKey, err3 := database.GetConfig("secretKey")

			if err1 != nil || err2 != nil || err3 != nil {
				errorMsg = "错误: 无法从数据库加载API配置。"
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, reportType, dstPath, startTime, time.Now(), time.Now(), "failure", errorMsg)
				return
			}

			client := api.NewClient(baseURL, secretId, secretKey)

			// 打开Excel文件
			logger.Log.WithFields(logrus.Fields{
				"filePath": filePath,
				"user":     a.user.Username,
			}).Info("正在处理Excel文件")

			var resp *api.CommonResponse
			var apiErr error

			// 根据报告类型确定处理方式
			processedReportType := reportType
			if strings.Contains(reportType, "价格数据") {
				processedReportType = "价格日报"
			} else if strings.Contains(reportType, "进销存数据") {
				processedReportType = "库存日报"
			}

			// 3. 解析Excel文件并提交数据
			endTime := time.Now()

			// 使用excelize库打开Excel文件
			f, err := excelize.OpenFile(filePath)
			if err != nil {
				errorMsg = fmt.Sprintf("打开Excel文件失败: %v", err)
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
				return
			}
			defer f.Close()

			// 获取第一个工作表
			sheetName := f.GetSheetName(0)
			if sheetName == "" {
				errorMsg = "Excel文件中没有工作表"
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
				return
			}

			// 读取所有行
			rows, err := f.GetRows(sheetName)
			if err != nil {
				errorMsg = fmt.Sprintf("读取Excel数据失败: %v", err)
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
				return
			}

			// 检查是否有数据
			if len(rows) < 2 { // 至少需要标题行和一行数据
				errorMsg = "Excel文件中没有足够的数据行"
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
				return
			}

			switch processedReportType {
			case "价格日报":
				// 解析价格报告Excel
				request, err := parsePriceReportExcel(rows, a.user.Username)
				if err != nil {
					errorMsg = fmt.Sprintf("解析Excel数据失败: %v", err)
					updateUI(errorMsg, true)
					saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
					return
				}

				// 提交价格数据
				resp, apiErr = client.SubmitPriceReport(request)

			case "库存日报":
				// 解析库存报告Excel
				request, err := parseInventoryReportExcel(rows, a.user.Username)
				if err != nil {
					errorMsg = fmt.Sprintf("解析Excel数据失败: %v", err)
					updateUI(errorMsg, true)
					saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
					return
				}

				// 提交库存数据
				resp, apiErr = client.SubmitInventoryReport(request)

			default:
				errorMsg = fmt.Sprintf("不支持的报告类型: %s", reportType)
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, time.Now(), "failure", errorMsg)
				return
			}

			// 4. 处理API响应
			submitTime := time.Now()
			if apiErr != nil {
				errorMsg = fmt.Sprintf("API提交失败: %v", apiErr)
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, submitTime, "failure", errorMsg)
				return
			}

			if resp.Code.String() != "0" {
				errorMsg = fmt.Sprintf("API返回错误: %s", resp.Message)
				updateUI(errorMsg, true)
				saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, submitTime, "failure", errorMsg)
				return
			}

			// 5. 显示成功消息
			successMsg := fmt.Sprintf("提交成功! 处理时间: %v, API响应: %s", submitTime.Sub(startTime), resp.Message)

			// 检查是否有错误文件需要下载
			if lastErrorFilePath != "" {
				successMsg += "\n\n⚠️ 注意：部分数据存在错误，已生成错误数据文件。"
				updateUI(successMsg, false)

				// 显示下载错误文件的对话框
				go func() {
					time.Sleep(500 * time.Millisecond) // 稍等一下再显示对话框
					showErrorFileDownloadDialog(a, lastErrorFilePath)
				}()
			} else {
				updateUI(successMsg, false)
			}

			// 6. 保存提交记录
			saveSubmission(a.user.ID, processedReportType, dstPath, startTime, endTime, submitTime, "success", "")

			// 7. 显示关联品类信息
			go func() {
				showAssociatedCategoriesInfo(a, processedReportType)
			}()
		}()
	})

	// 创建顶部表单区域
	formArea := container.NewVBox(
		widget.NewForm(
			widget.NewFormItem("上报类型", reportTypeSelect),
			widget.NewFormItem("数据文件", container.NewBorder(nil, nil, nil, fileSelectButton, filePathEntry)),
		),
		container.NewCenter(submitButton),
	)

	// 使用 Border 布局，让滚动区域铺满剩余空间
	return container.NewBorder(
		formArea,                          // 顶部：表单和按钮
		nil,                               // 底部：无
		nil,                               // 左侧：无
		nil,                               // 右侧：无
		container.NewVScroll(resultLabel), // 中心：滚动区域铺满剩余空间
	)
}

func showAssociatedCategoriesInfo(a *App, reportType string) {
	if reportType == "" {
		return
	}

	// 获取关联的品类
	categories, err := database.GetAssociatedCategories(reportType)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":      err,
			"reportType": reportType,
			"user":       a.user.Username,
		}).Error("获取关联品类失败")
		return
	}

	// 如果没有关联品类，可以提示用户
	if len(categories) == 0 {
		// 可以在这里添加提示逻辑
		return
	}

	// 构建关联品类信息字符串
	var infoBuilder strings.Builder
	infoBuilder.WriteString("关联品类: ")

	// 预分配足够的容量
	categoryNames := make([]string, 0, len(categories))
	for _, cat := range categories {
		categoryNames = append(categoryNames, cat.CategoryName)
	}

	// 使用strings.Join高效连接字符串
	infoBuilder.WriteString(strings.Join(categoryNames, ", "))

	// 显示关联品类信息
	// 这里可以添加显示逻辑，例如在状态栏或弹窗中显示
	// 示例：在控制台输出
	logger.Log.WithFields(logrus.Fields{
		"reportType": reportType,
		"categories": strings.Join(categoryNames, ", "),
		"user":       a.user.Username,
	}).Info("显示关联品类信息")
}

func saveSubmission(userID int, reportType, filePath string, startTime, endTime, resultTime time.Time, status, errorMsg string) {
	err := database.AddSubmission(uint(userID), reportType, filePath, startTime, endTime, resultTime, status, errorMsg)
	// _, err := db.Exec(`INSERT INTO submissions (user_id, report_type, file_path, start_time, end_time, result_time, status, error_message) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
	// 	userID, reportType, filePath, startTime, endTime, resultTime, status, errorMsg)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("保存提交记录失败")
	}
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destinationFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destinationFile.Close()

	_, err = io.Copy(destinationFile, sourceFile)
	return err
}

// parsePriceReportExcel 解析价格日报Excel数据
func parsePriceReportExcel(rows [][]string, username string) (api.PriceReportRequest, error) {
	var request api.PriceReportRequest
	// 预分配内存，避免频繁扩容
	request.Items = make([]api.PriceReportItem, 0, len(rows)-1)

	// 获取当前时间格式化字符串，避免在循环中重复计算
	now := time.Now().Format("2006-01-02 15:04:05")

	// 确保至少需要8列（索引0-7）
	const minColumnsRequired = 8

	// 跳过标题行
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 检查行是否有足够的列
		if len(row) < minColumnsRequired {
			continue // 跳过数据不完整的行
		}

		// 安全访问列数据：使用实际列数或空字符串
		getSafe := func(index int) string {
			if index < len(row) {
				return strings.TrimSpace(row[index])
			}
			return "" // 列不存在时返回空值
		}

		// 创建一个新的价格报告项
		item := api.PriceReportItem{
			LoginName:    username,
			CommodityID:  getSafe(0),
			Price:        getSafe(1),
			RptDate:      getSafe(2),
			CreateTime:   now,
			UpdateTime:   now,
			Linkman:      getSafe(3),
			Telephone:    getSafe(4),
			Memo:         getSafe(5),
			Leader:       getSafe(6),
			Statistician: getSafe(7),
		}

		// 添加到请求中
		request.Items = append(request.Items, item)
	}

	// 检查是否有数据
	if len(request.Items) == 0 {
		return request, fmt.Errorf("没有有效的价格报告数据")
	}

	return request, nil
}

// validateOriginPlace 验证产地是否在行政区划中存在
func validateOriginPlace(originPlace string) bool {
	if originPlace == "" || len(originPlace) < 6 {
		return false
	}

	// 查询数据库中是否存在该产地
	var count int64
	err := database.DB.Model(&models.Re{}).
		Where("unit_name LIKE ? AND is_deleted = ?", "%"+originPlace+"%", 0).
		Count(&count).Error

	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":       err,
			"originPlace": originPlace,
		}).Error("查询产地失败")
		return false
	}

	return count > 0
}

// ErrorRow 错误行数据结构
type ErrorRow struct {
	RowData     []string // 原始行数据
	ErrorReason string   // 错误原因
	RowNumber   int      // 行号
}

// createErrorExcel 创建错误Excel文件
func createErrorExcel(errorRows []ErrorRow, originalHeaders []string) (string, error) {
	f := excelize.NewFile()
	defer f.Close()

	// 创建错误数据工作表
	sheetName := "错误数据"
	f.NewSheet(sheetName)
	f.DeleteSheet("Sheet1") // 删除默认工作表

	// 设置表头（原始列 + 错误原因列）
	headers := append(originalHeaders, "错误原因")
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"FFFF00"}, Pattern: 1},
	})
	headerRange := fmt.Sprintf("A1:%s1", string(rune('A'+len(headers)-1)))
	f.SetCellStyle(sheetName, "A1", headerRange, headerStyle)

	// 填充错误数据
	for i, errorRow := range errorRows {
		rowNum := i + 2 // 从第2行开始（第1行是表头）

		// 填充原始数据
		for j, cellData := range errorRow.RowData {
			cell, _ := excelize.CoordinatesToCellName(j+1, rowNum)
			f.SetCellValue(sheetName, cell, cellData)
		}

		// 填充错误原因
		errorCell, _ := excelize.CoordinatesToCellName(len(headers), rowNum)
		f.SetCellValue(sheetName, errorCell, errorRow.ErrorReason)
	}

	// 设置错误原因列样式
	errorColStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Color: "FF0000"}, // 红色字体
	})
	errorColLetter := string(rune('A' + len(headers) - 1))
	errorColRange := fmt.Sprintf("%s2:%s%d", errorColLetter, errorColLetter, len(errorRows)+1)
	f.SetCellStyle(sheetName, errorColRange, errorColRange, errorColStyle)

	// 保存文件
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("错误数据_%s.xlsx", timestamp)
	filePath := filepath.Join(os.TempDir(), fileName)

	err := f.SaveAs(filePath)
	if err != nil {
		return "", fmt.Errorf("保存错误Excel文件失败: %v", err)
	}

	return filePath, nil
}

// showErrorFileDownloadDialog 显示错误文件下载对话框
func showErrorFileDownloadDialog(a *App, errorFilePath string) {
	if errorFilePath == "" {
		return
	}

	// 创建对话框内容
	content := widget.NewLabel("检测到部分数据存在产地错误，已生成错误数据文件。\n您可以下载此文件查看具体的错误信息。")
	content.Wrapping = fyne.TextWrapWord

	// 创建下载按钮
	downloadBtn := widget.NewButton("下载错误文件", func() {
		// 打开文件保存对话框
		saveDialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
			if writer == nil {
				return
			}

			// 复制错误文件到用户选择的位置
			go func() {
				dstPath := writer.URI().Path()
				err := copyErrorFile(errorFilePath, dstPath)
				if err != nil {
					dialog.ShowError(fmt.Errorf("保存文件失败: %v", err), a.mainWin)
				} else {
					dialog.ShowInformation("下载完成", "错误数据文件已保存成功！", a.mainWin)
				}
			}()
		}, a.mainWin)

		// 设置默认文件名
		timestamp := time.Now().Format("20060102_150405")
		defaultName := fmt.Sprintf("错误数据_%s.xlsx", timestamp)
		saveDialog.SetFileName(defaultName)
		saveDialog.Show()
	})

	// 创建稍后处理按钮
	laterBtn := widget.NewButton("稍后处理", func() {
		// 关闭对话框，不做任何操作
	})

	// 创建按钮容器
	buttons := container.NewHBox(downloadBtn, laterBtn)

	// 创建对话框容器
	dialogContent := container.NewVBox(
		content,
		widget.NewSeparator(),
		buttons,
	)

	// 显示自定义对话框
	d := dialog.NewCustom("错误数据文件", "关闭", dialogContent, a.mainWin)
	d.Resize(fyne.NewSize(400, 200))
	d.Show()
}

// copyErrorFile 复制错误文件
func copyErrorFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	return err
}

// parseInventoryReportExcel 解析库存日报Excel数据
func parseInventoryReportExcel(rows [][]string, username string) (api.InventoryReportRequest, error) {
	var request api.InventoryReportRequest
	// 预分配内存，避免频繁扩容
	request.Items = make([]api.InventoryReportItem, 0, len(rows)-1)

	// 获取当前时间格式化字符串，避免在循环中重复计算
	now := time.Now().Format("2006-01-02 15:04:05")

	// 确保至少需要13列（索引0-12）
	const minColumnsRequired = 13

	// 错误行收集
	var errorRows []ErrorRow
	var originalHeaders []string
	if len(rows) > 0 {
		originalHeaders = rows[0] // 保存原始表头
	}

	// 跳过标题行
	for i := 1; i < len(rows); i++ {
		row := rows[i]
		// 检查行是否有足够的列
		if len(row) < minColumnsRequired {
			continue // 跳过数据不完整的行
		}

		// 安全访问列数据：使用实际列数或空字符串
		getSafe := func(index int) string {
			if index < len(row) {
				return strings.TrimSpace(row[index])
			}
			return "" // 列不存在时返回空值
		}

		OriginPlace := getSafe(10)

		// 验证产地
		var errorReason string
		if OriginPlace == "" {
			errorReason = "产地不能为空"
		} else if len(OriginPlace) < 6 {
			errorReason = "产地长度不能少于6个字符"
		} else if !validateOriginPlace(OriginPlace) {
			errorReason = "产地不符合要求：在行政区划中未找到匹配项"
		}

		// 如果有错误，记录错误行
		if errorReason != "" {
			errorRows = append(errorRows, ErrorRow{
				RowData:     row,
				ErrorReason: errorReason,
				RowNumber:   i + 1, // Excel行号从1开始
			})

			logger.Log.WithFields(logrus.Fields{
				"row":         i + 1,
				"originPlace": OriginPlace,
				"error":       errorReason,
			}).Warn("发现产地错误数据")

			continue // 跳过错误数据，不添加到请求中
		}

		// 创建库存报告项（显式处理每列）
		item := api.InventoryReportItem{
			LoginName:           username,
			CommodityID:         getSafe(0),
			Stock:               getSafe(1),
			Amount:              getSafe(2),
			InStock:             getSafe(3),
			RptDate:             getSafe(4),
			CreateTime:          now,
			UpdateTime:          now,
			Linkman:             getSafe(5),
			Telephone:           getSafe(6),
			Memo:                getSafe(7),
			Leader:              getSafe(8),
			Statistician:        getSafe(9),
			OriginPlace:         OriginPlace,
			ManufacturerName:    getSafe(11),
			ManufacturerAddress: getSafe(12),
		}

		request.Items = append(request.Items, item)
	}

	// 如果有错误数据，创建错误Excel文件
	if len(errorRows) > 0 {
		errorFilePath, err := createErrorExcel(errorRows, originalHeaders)
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error":      err,
				"errorCount": len(errorRows),
			}).Error("创建错误Excel文件失败")
		} else {
			// 存储错误文件路径到全局变量
			lastErrorFilePath = errorFilePath

			logger.Log.WithFields(logrus.Fields{
				"errorFilePath": errorFilePath,
				"errorCount":    len(errorRows),
				"validCount":    len(request.Items),
			}).Info("已创建错误数据Excel文件")
		}
	} else {
		// 清空之前的错误文件路径
		lastErrorFilePath = ""
	}

	if len(request.Items) == 0 {
		if len(errorRows) > 0 {
			return request, fmt.Errorf("所有数据都有错误，请检查错误Excel文件")
		}
		return request, fmt.Errorf("没有有效的库存报告数据")
	}

	logger.Log.WithFields(logrus.Fields{
		"validCount": len(request.Items),
		"errorCount": len(errorRows),
	}).Info("库存报告数据解析完成")

	return request, nil
}

// func parseInventoryReportExcel(rows [][]string) (api.InventoryReportRequest, error) {
// 	var request api.InventoryReportRequest
// 	loginName, _ := database.GetConfig("loginName")
// 	// 跳过标题行
// 	for i := 1; i < len(rows); i++ {
// 		row := rows[i]
// 		// 检查行是否有足够的列
// 		if len(row) < 10 { // 根据InventoryReportItem的字段数量调整
// 			continue // 跳过数据不完整的行
// 		}

// 		// 创建一个新的库存报告项
// 		item := api.InventoryReportItem{
// 			LoginName:           loginName, // 假设第1列是LoginName
// 			CommodityID:         row[0],    // 假设第2列是CommodityID
// 			Stock:               row[1],    // 假设第3列是Stock
// 			Amount:              row[2],    // 假设第4列是Amount
// 			InStock:             row[3],    // 假设第5列是InStock
// 			RptDate:             row[4],    // 假设第6列是RptDate
// 			CreateTime:          time.Now().Format("2006-01-02 15:04:05"),
// 			UpdateTime:          time.Now().Format("2006-01-02 15:04:05"),
// 			Linkman:             row[5],  // 假设第7列是Linkman
// 			Telephone:           row[6],  // 假设第8列是Telephone
// 			Memo:                row[7],  // 假设第9列是Memo
// 			Leader:              row[8],  // 假设第10列是Leader
// 			Statistician:        row[9],  // 假设第11列是Statistician
// 			OriginPlace:         row[10], // 假设第12列是OriginPlace
// 			ManufacturerName:    row[11], // 假设第13列是ManufacturerName
// 			ManufacturerAddress: row[12], // 假设第14列是ManufacturerAddress
// 		}

// 		// 添加到请求中
// 		request.Items = append(request.Items, item)
// 	}

// 	// 检查是否有数据
// 	if len(request.Items) == 0 {
// 		return request, fmt.Errorf("没有有效的库存报告数据")
// 	}

// 	return request, nil
// }
