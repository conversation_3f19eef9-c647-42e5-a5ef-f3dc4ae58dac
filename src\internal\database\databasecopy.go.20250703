package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"simple_inventory_management_system/api"

	_ "github.com/mutecomm/go-sqlcipher/v4"
	"golang.org/x/crypto/bcrypt"
)

var DB *sql.DB

// 数据库加密密钥
var encryptionKey string

type Submission struct {
	ID         int
	UserID     int
	Username   string
	ReportType string
	FilePath   string
	StartTime  time.Time
	EndTime    time.Time
	ResultTime time.Time
	Status     string
	ErrorMsg   sql.NullString
	CreatedAt  time.Time
}

// InitDB 初始化加密数据库
func InitDB() error {
	// 从环境变量读取密钥
	encryptionKey = os.Getenv("DB_ENCRYPTION_KEY")
	if encryptionKey == "" {
		encryptionKey = ">M38rG-sMrG@m4vV"
		// return fmt.Errorf("请通过环境变量 DB_ENCRYPTION_KEY 设置数据库加密密钥")
	}
	// 确保数据库目录存在
	dbDir := "data"
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 数据库文件路径
	dbPath := filepath.Join(dbDir, "inventory.db")

	// 构建数据库连接字符串，包含加密参数
	dsn := fmt.Sprintf("%s?_pragma_key=x'%s'&_pragma_cipher_page_size=4096", dbPath, encryptionKey)

	// 打开数据库连接
	var err error
	DB, err = sql.Open("sqlite3", dsn)
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	// 测试数据库连接
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("连接数据库失败: %v", err)
	}

	// 创建表
	if err = createTables(); err != nil {
		return fmt.Errorf("创建表失败: %v", err)
	}

	// 初始化基础数据
	if err = seedData(); err != nil {
		return fmt.Errorf("初始化数据失败: %v", err)
	}

	log.Println("数据库初始化成功")
	return nil
}

// createTables 创建必要的数据表
func createTables() error {
	// 创建角色表
	_, err := DB.Exec(`
		CREATE TABLE IF NOT EXISTS roles (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name TEXT NOT NULL UNIQUE
		)
	`)
	if err != nil {
		return err
	}

	// 创建用户表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS users (
		    id INTEGER PRIMARY KEY AUTOINCREMENT,
		    username TEXT NOT NULL UNIQUE,
		    password_hash TEXT NOT NULL, 
		    role_id INTEGER,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP 
		)
	`)
	if err != nil {
		return err
	}

	// 创建配置表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS configs (
			key TEXT PRIMARY KEY,
			value TEXT NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return err
	}

	// 创建品类配置表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS categories (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			category_type TEXT NOT NULL,
			category_code TEXT NOT NULL,
			category_name TEXT NOT NULL,
			parent_code TEXT,
			industry_type TEXT,
			price_unit TEXT,
			quantity_unit TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return err
	}

	// 创建地区名称代码表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS regions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			code TEXT NOT NULL UNIQUE,
			name TEXT NOT NULL,
			is_deleted INTEGER DEFAULT 0,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return err
	}

	// 创建行政区划字典表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS admin_divisions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			code TEXT NOT NULL UNIQUE,
			unit_name TEXT NOT NULL,
			is_deleted INTEGER DEFAULT 0,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return err
	}

	// 创建提交记录表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS submissions (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			user_id INTEGER,
			report_type TEXT NOT NULL,
			file_path TEXT NOT NULL,
			request_body TEXT,
			response_code INTEGER,
			response_msg TEXT,
			request_id TEXT,
			start_time DATETIME,
			end_time DATETIME,
			result_time DATETIME,
			status TEXT,
			error_msg TEXT,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP
		)
	`)
	if err != nil {
		return err
	}

	// 创建上报类型与品类关联表
	_, err = DB.Exec(`
		CREATE TABLE IF NOT EXISTS report_category_associations (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			report_type TEXT NOT NULL,
			category_code TEXT NOT NULL,
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(report_type, category_code)
		)
	`)
	if err != nil {
		return err
	}

	return nil
}

// GetDB 返回数据库连接
func GetDB() *sql.DB {
	return DB
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}

// Config 结构体用于表示配置项
type Config struct {
	Key   string
	Value string
}

// GetAllConfigs 获取所有配置项
func GetAllConfigs() ([]Config, error) {
	rows, err := DB.Query("SELECT key, value FROM configs")
	if err != nil {
		return nil, fmt.Errorf("查询所有配置失败: %w", err)
	}
	defer rows.Close()

	var configs []Config
	for rows.Next() {
		var c Config
		if err := rows.Scan(&c.Key, &c.Value); err != nil {
			return nil, fmt.Errorf("扫描配置失败: %w", err)
		}
		configs = append(configs, c)
	}
	return configs, nil
}

// GetConfig 根据 key 获取配置值
func GetConfig(key string) (string, error) {
	var value string
	err := DB.QueryRow("SELECT value FROM configs WHERE key = ?", key).Scan(&value)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("配置项 %s 不存在", key)
		}
		return "", fmt.Errorf("查询配置项 %s 失败: %w", key, err)
	}
	return value, nil
}

// UpdateConfig 更新配置项
func UpdateConfig(key, value string) error {
	_, err := DB.Exec("UPDATE configs SET value = ? WHERE key = ?", value, key)
	if err != nil {
		return fmt.Errorf("更新配置项 %s 失败: %w", key, err)
	}
	return nil
}

// AddConfig 添加新配置项
func AddConfig(key, value string) error {
	_, err := DB.Exec("INSERT INTO configs (key, value) VALUES (?, ?)", key, value)
	if err != nil {
		return fmt.Errorf("添加配置项 %s 失败: %w", key, err)
	}
	return nil
}

// DeleteConfig 删除配置项
func DeleteConfig(key string) error {
	_, err := DB.Exec("DELETE FROM configs WHERE key = ?", key)
	if err != nil {
		return fmt.Errorf("删除配置项 %s 失败: %w", key, err)
	}
	return nil
}

func GetAdminDivisionsCount(unitName string) (int, error) {
	var count int
	query := "SELECT count(*) FROM admin_divisions WHERE is_deleted = 0"
	args := []interface{}{}
	if unitName != "" {
		query += " AND unit_name LIKE ?"
		args = append(args, "%"+unitName+"%")
	}
	err := DB.QueryRow(query, args...).Scan(&count)
	return count, err
}

// SearchAdminDivisions searches for admin divisions by unit name.
func SearchAdminDivisions(unitName string, pageSize, pageNum int) ([]api.AdminDivision, int, error) {
	count, err := GetAdminDivisionsCount(unitName)
	if err != nil {
		return nil, 0, err
	}
	query := "SELECT id, code, unit_name, is_deleted FROM admin_divisions WHERE is_deleted = 0"
	args := []interface{}{}
	if unitName != "" {
		query += " AND unit_name LIKE ?"
		args = append(args, "%"+unitName+"%")
	}
	query += " limit ? offset ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)
	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询行政区划失败: %w", err)
	}
	defer rows.Close()

	var divisions []api.AdminDivision
	for rows.Next() {
		var d api.AdminDivision
		if err := rows.Scan(&d.ID, &d.Code, &d.UnitName, &d.IsDeleted); err == nil {
			divisions = append(divisions, d)
		} else {
			log.Printf("Error scanning admin division: %v\n", err)
			// return nil, 0, fmt.Errorf("扫描行政区划失败: %w", err)
		}
	}
	return divisions, count, nil
}

// ClearAdminDivisions clears all data from the admin_divisions table.
func ClearAdminDivisions() error {
	_, err := DB.Exec("DELETE FROM admin_divisions")
	if err != nil {
		return fmt.Errorf("清空行政区划数据失败: %w", err)
	}
	return nil
}

func GetRegionsCount(name string) (int, error) {
	var count int
	query := "SELECT count(*) FROM regions WHERE is_deleted = 0"
	args := []interface{}{}
	if name != "" {
		query += " AND name LIKE ?"
		args = append(args, "%"+name+"%")
	}
	err := DB.QueryRow(query, args...).Scan(&count)
	return count, err
}

// SearchRegions searches for regions by name.
func SearchRegions(name string, pageSize, pageNum int) ([]api.Region, int, error) {
	count, err := GetRegionsCount(name)
	if err != nil {
		return nil, 0, err
	}
	query := "SELECT id, code, name, is_deleted FROM regions WHERE is_deleted = 0"
	args := []interface{}{}
	if name != "" {
		query += " AND name LIKE ?"
		args = append(args, "%"+name+"%")
	}
	query += " limit ? offset ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)
	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询地区失败: %w", err)
	}
	defer rows.Close()

	var regions []api.Region
	for rows.Next() {
		var r api.Region
		if err := rows.Scan(&r.ID, &r.Code, &r.Name, &r.IsDeleted); err != nil {
			return nil, 0, fmt.Errorf("扫描地区失败: %w", err)
		}
		regions = append(regions, r)
	}
	return regions, count, nil
}

// ClearRegions clears all data from the regions table.
func ClearRegions() error {
	_, err := DB.Exec("DELETE FROM regions")
	if err != nil {
		return fmt.Errorf("清空地区代码数据失败: %w", err)
	}
	return nil
}

func GetCategoriesCount(categoryCode, categoryType, categoryName string) (int, error) {
	var count int
	query := "SELECT COUNT(*) FROM categories"
	args := []interface{}{}
	var conditions []string

	if categoryCode != "" {
		conditions = append(conditions, "category_code LIKE ?")
		args = append(args, categoryCode+"%")
	}
	if categoryType != "" {
		conditions = append(conditions, "industry_type LIKE ?")
		args = append(args, "%"+categoryType+"%")
	}
	if categoryName != "" {
		conditions = append(conditions, "category_name LIKE ?")
		args = append(args, "%"+categoryName+"%")
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}
	err := DB.QueryRow(query, args...).Scan(&count)
	return count, err
}

// SearchCategories searches for categories by code, type, and name.
func SearchCategories(categoryCode, categoryType, categoryName string, pageSize, pageNum int) ([]api.Category, int, error) {
	count, err := GetCategoriesCount(categoryCode, categoryType, categoryName)
	if err != nil {
		return nil, 0, err
	}
	query := "SELECT id, category_code, category_name, parent_code, industry_type, price_unit, quantity_unit FROM categories"
	args := []interface{}{}
	var conditions []string

	if categoryCode != "" {
		conditions = append(conditions, "category_code LIKE ?")
		args = append(args, categoryCode+"%")
	}
	if categoryType != "" {
		conditions = append(conditions, "industry_type LIKE ?")
		args = append(args, "%"+categoryType+"%")
	}
	if categoryName != "" {
		conditions = append(conditions, "category_name LIKE ?")
		args = append(args, "%"+categoryName+"%")
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}
	query += " limit ? offset ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)
	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询品类失败: %w", err)
	}
	defer rows.Close()

	var categories []api.Category
	for rows.Next() {
		var c api.Category
		if err := rows.Scan(&c.ID, &c.CategoryCode, &c.CategoryName, &c.ParentCode, &c.IndustryType, &c.PriceUnit, &c.QuantityUnit); err != nil {
			return nil, 0, fmt.Errorf("扫描品类失败: %w", err)
		}
		categories = append(categories, c)
	}
	return categories, count, nil
}

func seedData() error {
	// 初始化角色
	roles := []string{"管理员", "普通员工"}
	for _, roleName := range roles {
		var id int
		err := DB.QueryRow("SELECT id FROM roles WHERE name = ?", roleName).Scan(&id)
		if err == sql.ErrNoRows {
			_, err := DB.Exec("INSERT INTO roles (name) VALUES (?)", roleName)
			if err != nil {
				return err
			}
		}
	}

	// 初始化管理员用户
	var userID int
	err := DB.QueryRow("SELECT id FROM users WHERE username = ?", "admin").Scan(&userID)
	if err == sql.ErrNoRows {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte("admin@123"), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		var roleID int
		err = DB.QueryRow("SELECT id FROM roles WHERE name = ?", "管理员").Scan(&roleID)
		if err != nil {
			return err
		}

		// 修改用户初始化代码
		_, err = DB.Exec("INSERT INTO users (username, password_hash, role_id) VALUES (?, ?, ?)",
			"admin", string(hashedPassword), roleID)

		// _, err = DB.Exec("INSERT INTO users (username, password, role_id) VALUES (?, ?, ?)", "admin", string(hashedPassword), roleID)
		if err != nil {
			return err
		}
	}

	// 初始化配置
	configs := map[string]string{
		"baseURL":   "http://58.48.136.183:19090/hbyjbg-api/model/v2/",
		"secretId":  "your_secret_id",
		"secretKey": "your_secret_key",
	}
	for key, value := range configs {
		var dbValue string
		err := DB.QueryRow("SELECT value FROM configs WHERE key = ?", key).Scan(&dbValue)
		if err == sql.ErrNoRows {
			_, err := DB.Exec("INSERT INTO configs (key, value) VALUES (?, ?)", key, value)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func GetSubmissionsCount() (int, error) {
	var count int
	err := DB.QueryRow("SELECT COUNT(*) FROM submissions").Scan(&count)
	return count, err
}

func GetSubmissions(page, pageSize int) ([]Submission, error) {
	offset := (page - 1) * pageSize
	rows, err := DB.Query("SELECT ss.id, ss.user_id,users.username, ss.report_type, ss.file_path, ss.start_time, ss.end_time, ss.result_time, ss.status, ss.error_msg, ss.created_at FROM submissions ss left join users users on users.id = ss.user_id  ORDER BY created_at DESC LIMIT ? OFFSET ?", pageSize, offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var submissions []Submission
	for rows.Next() {
		var s Submission
		if err := rows.Scan(&s.ID, &s.UserID, &s.ReportType, &s.FilePath, &s.StartTime, &s.EndTime, &s.ResultTime, &s.Status, &s.ErrorMsg, &s.CreatedAt); err != nil {
			return nil, err
		}
		submissions = append(submissions, s)
	}

	return submissions, nil
}

// ClearCategories clears all data from the categories table.
func ClearCategories() error {
	_, err := DB.Exec("DELETE FROM categories")
	if err != nil {
		return fmt.Errorf("清空品类数据失败: %w", err)
	}
	return nil
}

func GetUsers(username string, pageSize, pageNum int) ([]api.User, int, error) {
	// 查询总数
	var count int
	query := "SELECT count(*) FROM users"
	args := []interface{}{}

	if username != "" {
		query += " WHERE username LIKE ?"
		args = append(args, "%"+username+"%")
	}

	err := DB.QueryRow(query, args...).Scan(&count)
	if err != nil {
		return nil, 0, err
	}

	// 查询用户列表
	query = "SELECT u.id, u.username, u.role_id, r.name,u.created_at FROM users u LEFT JOIN roles r ON u.role_id = r.id"
	args = []interface{}{}

	if username != "" {
		query += " WHERE u.username LIKE ?"
		args = append(args, "%"+username+"%")
	}

	query += " LIMIT ? OFFSET ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)

	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("查询用户失败: %w", err)
	}
	defer rows.Close()

	var users []api.User
	for rows.Next() {
		var user api.User
		var roleName string
		if err := rows.Scan(&user.ID, &user.Username, &user.RoleID, &roleName, &user.CreatedAt); err != nil {
			return nil, 0, fmt.Errorf("扫描用户数据失败: %w", err)
		}
		user.Role = roleName
		users = append(users, user)
	}

	return users, count, nil
}

// AddUser 添加用户
func AddUser(username, password, roleName string) error {
	// 查询角色ID
	var roleID int
	err := DB.QueryRow("SELECT id FROM roles WHERE name = ?", roleName).Scan(&roleID)
	if err != nil {
		return fmt.Errorf("查询角色失败: %w", err)
	}

	// 生成密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成密码哈希失败: %w", err)
	}

	// 插入用户
	_, err = DB.Exec("INSERT INTO users (username, password_hash, role_id) VALUES (?, ?, ?)",
		username, string(hashedPassword), roleID)
	if err != nil {
		return fmt.Errorf("添加用户失败: %w", err)
	}

	return nil
}

// DeleteUser 删除用户
func DeleteUser(userID int) error {
	_, err := DB.Exec("DELETE FROM users WHERE id = ?", userID)
	if err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}
	return nil
}

// AddReportCategoryAssociation 添加上报类型与品类的关联
func AddReportCategoryAssociation(reportType, categoryCode string) error {
	_, err := DB.Exec("INSERT OR REPLACE INTO report_category_associations (report_type, category_code) VALUES (?, ?)",
		reportType, categoryCode)
	if err != nil {
		return fmt.Errorf("添加上报类型与品类关联失败: %w", err)
	}
	return nil
}

// RemoveReportCategoryAssociation 移除上报类型与品类的关联
func RemoveReportCategoryAssociation(reportType, categoryCode string) error {
	_, err := DB.Exec("DELETE FROM report_category_associations WHERE report_type = ? AND category_code = ?",
		reportType, categoryCode)
	if err != nil {
		return fmt.Errorf("移除上报类型与品类关联失败: %w", err)
	}
	return nil
}

// GetAssociatedCategories 获取与上报类型关联的品类
func GetAssociatedCategories(reportType string) ([]api.Category, error) {
	query := `
		SELECT c.id, c.category_code, c.category_name, c.parent_code, c.industry_type, c.price_unit, c.quantity_unit
		FROM categories c
		JOIN report_category_associations rca ON c.category_code = rca.category_code
		WHERE rca.report_type = ?
	`

	rows, err := DB.Query(query, reportType)
	if err != nil {
		return nil, fmt.Errorf("查询关联品类失败: %w", err)
	}
	defer rows.Close()

	var categories []api.Category
	for rows.Next() {
		var c api.Category
		if err := rows.Scan(&c.ID, &c.CategoryCode, &c.CategoryName, &c.ParentCode, &c.IndustryType, &c.PriceUnit, &c.QuantityUnit); err != nil {
			return nil, fmt.Errorf("扫描品类数据失败: %w", err)
		}
		categories = append(categories, c)
	}

	return categories, nil
}
