package ui

import (
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"simple_inventory_management_system/internal/models"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
)

func (a *App) createConfigTab() fyne.CanvasObject {
	var configs []models.Config
	
	// 加载配置函数
	loadConfig := func() {
		cfgs, err := database.GetAllConfigs()
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"user":  a.user.Username,
			}).Error("加载配置文件失败")
			return
		}
		configs = cfgs
	}
	
	// 初始加载配置
	loadConfig()
	
	// 创建表格
	table := widget.NewTable(
		func() (int, int) {
			rowCount := len(configs) + 1 // +1 for header
			if rowCount < 10 { // 确保至少显示10行
				rowCount = 10
			}
			return rowCount, 3 // 3 columns
		},
		func() fyne.CanvasObject {
			return container.NewStack(widget.NewLabel(""))
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			containerCell := obj.(*fyne.Container)
			label := containerCell.Objects[0].(*widget.Label)

			if id.Row == 0 {
				// Header row
				headers := []string{"配置项", "配置值", "操作"}
				if id.Col < len(headers) {
					label.SetText(headers[id.Col])
				}
				return
			}

			// Data rows
			if configs == nil || id.Row-1 >= len(configs) {
				label.SetText("")
				return
			}

			config := configs[id.Row-1]
			switch id.Col {
			case 0:
				label.SetText(config.Key)
			case 1:
				label.SetText(config.Value)
			case 2:
				label.SetText("编辑")
			}
		},
	)

	// 设置列宽
	table.SetColumnWidth(0, 200) // 配置项
	table.SetColumnWidth(1, 400) // 配置值 - 增加宽度以显示更多内容
	table.SetColumnWidth(2, 100) // 操作

	// 设置行高 - 只设置必要的行
	table.SetRowHeight(0, DEFAULT_ROW_HEIGHT) // 表头行高
	
	// 为数据行设置行高
	for i := 1; i <= len(configs); i++ {
		table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
	}
	
	// 为空行设置行高
	for i := len(configs) + 1; i < 10; i++ {
		table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
	}

	// 处理表格点击事件
	table.OnSelected = func(id widget.TableCellID) {
		if id.Row == 0 || id.Col != 2 { // 不是操作列或者是标题行
			return
		}
		if configs == nil || id.Row-1 >= len(configs) {
			return
		}
		config := configs[id.Row-1]
		a.showEditConfigDialog(config, func() {
			// 重新加载配置数据并刷新UI
			loadConfig()
			
			// 重新设置数据行高度
			for i := 1; i <= len(configs); i++ {
				table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
			}
			
			// 为空行设置行高
			for i := len(configs) + 1; i < 10; i++ {
				table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
			}
			
			table.Refresh()
		})
	}

	// 添加配置按钮
	addButton := widget.NewButton("添加配置", func() {
		a.showAddConfigDialog(func() {
			// 重新加载配置数据并刷新UI
			loadConfig()
			
			// 重新设置数据行高度
			for i := 1; i <= len(configs); i++ {
				table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
			}
			
			// 为空行设置行高
			for i := len(configs) + 1; i < 10; i++ {
				table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
			}
			
			table.Refresh()
		})
	})

	// 注册刷新函数
	a.refreshTabs["配置管理"] = loadConfig
	// 使用Border容器，让表格填充整个可用空间
	return container.NewBorder(
		container.NewHBox(addButton), // 顶部放置按钮
		nil,                          // 底部无内容
		nil,                          // 左侧无内容
		nil,                          // 右侧无内容
		table,                        // 中间放置表格，会自动填充剩余空间
	)
}

func (a *App) showEditConfigDialog(config models.Config, onComplete func()) {
	keyEntry := newFixedWidthEntry(200) //widget.NewEntry()
	keyEntry.SetText(config.Key)
	keyEntry.Disable() // 不允许修改key

	valueEntry := newFixedWidthEntry(200) //widget.NewEntry()
	valueEntry.SetText(config.Value)

	form := widget.NewForm(
		widget.NewFormItem("配置项", keyEntry),
		widget.NewFormItem("配置值", valueEntry),
	)

	dialog.ShowForm("编辑配置", "保存", "取消", form.Items, func(confirm bool) {
		if confirm {
			err := database.UpdateConfig(config.Key, valueEntry.Text)
			if err != nil {
				dialog.ShowError(err, a.mainWin)
			} else {
				onComplete()
			}
		}
	}, a.mainWin)
}

func (a *App) showAddConfigDialog(onComplete func()) {
	keyEntry := newFixedWidthEntry(200) //widget.NewEntry()
	keyEntry.SetPlaceHolder("请输入配置项名称")

	valueEntry := newFixedWidthEntry(200) //widget.NewEntry()
	valueEntry.SetPlaceHolder("请输入配置值")

	form := widget.NewForm(
		widget.NewFormItem("配置项", keyEntry),
		widget.NewFormItem("配置值", valueEntry),
	)

	dialog.ShowForm("添加配置", "保存", "取消", form.Items, func(confirm bool) {
		if confirm {
			if keyEntry.Text == "" || valueEntry.Text == "" {
				dialog.ShowInformation("错误", "配置项和配置值不能为空", a.mainWin)
				return
			}
			err := database.AddConfig(keyEntry.Text, valueEntry.Text)
			if err != nil {
				dialog.ShowError(err, a.mainWin)
			} else {
				onComplete()
			}
		}
	}, a.mainWin)
}
