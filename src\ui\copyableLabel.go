package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/widget"
)

type CopyableLabel struct {
	widget.Label
	hovered bool
}

func NewCopyableLabelWithoutText() *CopyableLabel {
	l := &CopyableLabel{}
	l.ExtendBaseWidget(l)
	return l
}

func NewCopyableLabel(text string) *CopyableLabel {
	l := &CopyableLabel{}
	l.ExtendBaseWidget(l)
	l.SetText(text)
	return l
}

func (l *CopyableLabel) SetText(text string) {
	l.Label.SetText(text)
}

// 实现 Tappable 接口
func (l *CopyableLabel) Tapped(pe *fyne.PointEvent) {
	// 左键点击复制
	clipboard := fyne.CurrentApp().Clipboard()
	clipboard.SetContent(l.Text)
}

// 实现 SecondaryTappable 接口
func (l *CopyableLabel) TappedSecondary(pe *fyne.PointEvent) {
	// 创建右键菜单
	menu := fyne.NewMenu("",
		fyne.NewMenuItem("复制", func() {
			clipboard := fyne.CurrentApp().Clipboard()
			clipboard.SetContent(l.Text)
		}),
	)

	// 显示右键菜单
	c := fyne.CurrentApp().Driver().CanvasForObject(l)
	widget.ShowPopUpMenuAtPosition(menu, c, pe.AbsolutePosition)
}

// 实现 Hoverable 接口
func (l *CopyableLabel) MouseIn(pe *desktop.MouseEvent) {
	l.hovered = true
	l.Refresh() // 刷新显示
}

func (l *CopyableLabel) MouseOut() {
	l.hovered = false
	l.Refresh() // 刷新显示
}

func (l *CopyableLabel) MouseMoved(pe *desktop.MouseEvent) {}

// 自定义渲染（不使用 LabelRenderer）
func (l *CopyableLabel) CreateRenderer() fyne.WidgetRenderer {
	// 使用基类的渲染器
	renderer := l.Label.CreateRenderer()

	return &copyableLabelRenderer{
		WidgetRenderer: renderer,
		label:          l,
	}
}

type copyableLabelRenderer struct {
	fyne.WidgetRenderer
	label *CopyableLabel
}

func (r *copyableLabelRenderer) Refresh() {
	r.WidgetRenderer.Refresh()

	// 添加悬停效果
	if r.label.hovered {
		// 改变文本颜色或添加下划线
		r.label.Label.TextStyle = fyne.TextStyle{Underline: true}
		// r.label.Label.Color = theme.PrimaryColor()
	} else {
		r.label.Label.TextStyle = fyne.TextStyle{}
		// r.label.Label.Color = theme.ForegroundColor()
	}
}

func (r *copyableLabelRenderer) Layout(size fyne.Size) {
	r.WidgetRenderer.Layout(size)
}

func (r *copyableLabelRenderer) MinSize() fyne.Size {
	return r.WidgetRenderer.MinSize()
}

func (r *copyableLabelRenderer) Objects() []fyne.CanvasObject {
	return r.WidgetRenderer.Objects()
}

func (r *copyableLabelRenderer) Destroy() {
	r.WidgetRenderer.Destroy()
}
