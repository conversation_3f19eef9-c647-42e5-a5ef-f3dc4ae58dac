package ai

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// OpenRouterClient OpenRouter API 客户端
type OpenRouterClient struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// Message 聊天消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// ChatRequest 聊天请求结构
type ChatRequest struct {
	Model       string    `json:"model"`
	Messages    []Message `json:"messages"`
	Temperature float64   `json:"temperature,omitempty"`
	MaxTokens   int       `json:"max_tokens,omitempty"`
	Stream      bool      `json:"stream,omitempty"`
}

// ChatResponse 聊天响应结构
type ChatResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
	Error *struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error,omitempty"`
}

// ModelInfo 模型信息
type ModelInfo struct {
	ID          string
	Name        string
	Description string
	Keywords    []string // 用于匹配用户消息的关键词
}

// NewOpenRouterClient 创建新的 OpenRouter 客户端
func NewOpenRouterClient(apiKey string) *OpenRouterClient {
	return &OpenRouterClient{
		APIKey:  apiKey,
		BaseURL: "https://openrouter.ai/api/v1",
		Client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// GetAvailableModels 获取可用的模型列表
func (c *OpenRouterClient) GetAvailableModels() []ModelInfo {
	return []ModelInfo{
		{
			ID:          "deepseek/deepseek-r1-0528:free",
			Name:        "DeepSeek R1",
			Description: "适合推理和逻辑分析",
			Keywords:    []string{"推理", "逻辑", "分析", "计算", "数学", "编程", "代码", "算法", "思考", "解决"},
		},
		{
			ID:          "qwen/qwen3-235b-a22b:free",
			Name:        "Qwen 3",
			Description: "适合中文对话和通用任务",
			Keywords:    []string{"中文", "对话", "聊天", "翻译", "写作", "文档", "总结", "解释", "帮助", "咨询"},
		},
		{
			ID:          "microsoft/mai-ds-r1:free",
			Name:        "Microsoft MAI",
			Description: "适合数据科学和技术分析",
			Keywords:    []string{"数据", "科学", "统计", "图表", "报表", "excel", "分析", "可视化", "技术", "系统"},
		},
		{
			ID:          "google/gemini-2.0-flash-exp:free",
			Name:        "Gemini 2.0 Flash",
			Description: "适合快速响应和多模态任务",
			Keywords:    []string{"快速", "图片", "图像", "视觉", "多媒体", "创意", "设计", "艺术", "生成", "创作"},
		},
	}
}

// SelectModel 根据用户消息选择最适合的模型
func (c *OpenRouterClient) SelectModel(userMessage string) string {
	models := c.GetAvailableModels()
	userMessageLower := strings.ToLower(userMessage)

	// 计算每个模型的匹配分数
	bestModel := models[2] // 默认使用 Qwen（中文对话）
	maxScore := 0

	for _, model := range models {
		score := 0
		for _, keyword := range model.Keywords {
			if strings.Contains(userMessageLower, keyword) {
				score++
			}
		}

		if score > maxScore {
			maxScore = score
			bestModel = model
		}
	}

	return bestModel.ID
}

// Chat 发送聊天请求
func (c *OpenRouterClient) Chat(messages []Message, model string) (*ChatResponse, error) {
	if model == "" {
		// 如果没有指定模型，根据最后一条用户消息选择
		if len(messages) > 0 {
			lastMessage := messages[len(messages)-1]
			if lastMessage.Role == "user" {
				model = c.SelectModel(lastMessage.Content)
			}
		}
		if model == "" {
			model = "qwen/qwen3-235b-a22b:free" // 默认模型
		}
	}

	request := ChatRequest{
		Model:       model,
		Messages:    messages,
		Temperature: 0.7,
		MaxTokens:   2000,
		Stream:      false,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.BaseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIKey)
	req.Header.Set("HTTP-Referer", "https://github.com/your-repo") // 可选：设置引用来源
	req.Header.Set("X-Title", "Inventory Management System")       // 可选：设置应用标题

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败 (状态码: %d): %s", resp.StatusCode, string(body))
	}

	var chatResponse ChatResponse
	if err := json.Unmarshal(body, &chatResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	if chatResponse.Error != nil {
		return nil, fmt.Errorf("API错误: %s", chatResponse.Error.Message)
	}

	return &chatResponse, nil
}

// GetModelName 根据模型ID获取友好的模型名称
func (c *OpenRouterClient) GetModelName(modelID string) string {
	models := c.GetAvailableModels()
	for _, model := range models {
		if model.ID == modelID {
			return model.Name
		}
	}
	return modelID
}
