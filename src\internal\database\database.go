package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"simple_inventory_management_system/internal/logger"
	"simple_inventory_management_system/internal/models"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormlogger "gorm.io/gorm/logger"
)

var DB *gorm.DB

// 数据库加密密钥
var encryptionKey string

// InitDB 初始化加密数据库
func InitDB() error {
	// 从环境变量读取密钥
	encryptionKey = os.Getenv("DB_ENCRYPTION_KEY")
	if encryptionKey == "" {
		encryptionKey = ">M38rG-sMrG@m4vV"
	}

	// 确保数据库目录存在
	dbDir := "data"
	if err := os.MkdirAll(dbDir, 0755); err != nil {
		return fmt.Errorf("创建数据库目录失败: %v", err)
	}

	// 数据库文件路径
	dbPath := filepath.Join(dbDir, "inventory.db")

	// if _, err := os.Stat(dbPath); err == nil {
	// 	if err := os.Chmod(dbPath, 0666); err != nil {
	// 		log.Println("警告: 无法更改文件权限", err)
	// 	}
	// }

	// 构建数据库连接字符串，包含加密参数
	dsn := fmt.Sprintf("%s?_pragma_key=x'%s'&_pragma_cipher_page_size=4096?journal_mode=WAL", dbPath, encryptionKey)

	// 打开数据库连接
	var err error
	DB, err = gorm.Open(sqlite.Open(dsn), &gorm.Config{
		Logger: gormlogger.New(
			log.New(logger.Log.Writer(), "\r\n", log.LstdFlags),
			gormlogger.Config{
				SlowThreshold:             time.Second,
				LogLevel:                  gormlogger.Info,
				IgnoreRecordNotFoundError: true,
				Colorful:                  false,
			},
		),
	})
	if err != nil {
		return fmt.Errorf("打开数据库失败: %v", err)
	}

	// 设置数据库连接池参数
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	// 自动迁移表
	if err = DB.AutoMigrate(
		&models.Role{},
		&models.User{},
		&models.Config{},
		&models.Category{},
		&models.Region{},
		&models.AdminDivision{},
		&models.Submission{},
		&models.ReportCategoryAssociation{},
	); err != nil {
		return fmt.Errorf("自动迁移表失败: %v", err)
	}

	// 初始化基础数据
	if err = seedData(); err != nil {
		return fmt.Errorf("初始化数据失败: %v", err)
	}

	log.Println("数据库初始化成功")
	return nil
}

// GetDB 返回数据库连接
func GetDB() *gorm.DB {
	return DB
}

// CloseDB 关闭数据库连接
func CloseDB() error {
	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetAllConfigs 获取所有配置项
func GetAllConfigs() ([]models.Config, error) {
	var configs []models.Config
	err := DB.Find(&configs).Error
	return configs, err
}

// GetConfig 根据 key 获取配置值
func GetConfig(key string) (string, error) {
	var config models.Config
	err := DB.Where("key = ?", key).First(&config).Error
	if err != nil {
		return "", err
	}
	return config.Value, nil
}

// UpdateConfig 更新配置项
func UpdateConfig(key, value string) error {
	return DB.Model(&models.Config{}).Where("key = ?", key).Update("value", value).Error
}

// AddConfig 添加新配置项
func AddConfig(key, value string) error {
	config := models.Config{Key: key, Value: value}
	return DB.Create(&config).Error
}

// DeleteConfig 删除配置项
func DeleteConfig(key string) error {
	return DB.Where("key = ?", key).Delete(&models.Config{}).Error
}

func AddAdminDivision(code, unitName string) error {
	division := models.AdminDivision{Code: code, UnitName: unitName}
	return DB.Create(&division).Error
}

func BatchAddAdminDivision(divisions []models.AdminDivision) error {
	return DB.CreateInBatches(divisions, len(divisions)).Error
}

func GetAdminDivisionsCount(unitName string) (int64, error) {
	var count int64
	query := DB.Model(&models.AdminDivision{}).Where("is_deleted = ?", 0)
	if unitName != "" {
		query = query.Where("unit_name LIKE ?", "%"+unitName+"%")
	}
	err := query.Count(&count).Error
	return count, err
}

// SearchAdminDivisions searches for admin divisions by unit name.
func SearchAdminDivisions(unitName string, pageSize, pageNum int) ([]models.AdminDivision, int64, error) {
	var divisions []models.AdminDivision
	var count int64

	query := DB.Model(&models.AdminDivision{}).Where("is_deleted = ?", 0)
	if unitName != "" {
		query = query.Where("unit_name LIKE ?", "%"+unitName+"%")
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Limit(pageSize).Offset((pageNum - 1) * pageSize).Find(&divisions).Error
	return divisions, count, err
}

// ClearAdminDivisions clears all data from the admin_divisions table.
func ClearAdminDivisions() error {
	return DB.Exec("DELETE FROM admin_divisions").Error
}

func AddRegions(code, name string) error {
	region := models.Region{Code: code, Name: name}
	return DB.Create(&region).Error
}

func BatchAddRegions(regions []models.Region) (int, error) {
	err := DB.CreateInBatches(regions, len(regions)).Error
	return len(regions), err
}

func GetRegionsCount(name string) (int64, error) {
	var count int64
	query := DB.Model(&models.Region{}).Where("is_deleted =?", 0)
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	err := query.Count(&count).Error
	return count, err
}

// SearchRegions searches for regions by name.
func SearchRegions(name string, pageSize, pageNum int) ([]models.Region, int64, error) {
	var regions []models.Region
	var count int64

	query := DB.Model(&models.Region{}).Where("is_deleted = ?", 0)
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Limit(pageSize).Offset((pageNum - 1) * pageSize).Find(&regions).Error
	return regions, count, err
}

// ClearRegions clears all data from the regions table.
func ClearRegions() error {
	return DB.Exec("DELETE FROM regions").Error
}

func AddCategory(categoryCode, categoryName, parentCode, industryType, priceUnit, quantityUnit, CategoryType string) error {
	category := models.Category{
		CategoryCode: categoryCode,
		CategoryType: CategoryType,
		CategoryName: categoryName,
		ParentCode:   parentCode,
		PriceUnit:    priceUnit,
		IndustryType: industryType,
		QuantityUnit: quantityUnit,
	}
	return DB.Create(&category).Error
}

func BatchAddCategory(categories []models.Category) error {
	return DB.CreateInBatches(categories, len(categories)).Error
}

func GetCategoriesCount(categoryCode, categoryType, categoryName string) (int64, error) {
	var count int64
	query := DB.Model(&models.Category{})
	if categoryCode != "" {
		query = query.Where("category_code LIKE ?", categoryCode+"%")
	}
	if categoryType != "" {
		query = query.Where("industry_type LIKE ?", "%"+categoryType+"%")
	}
	if categoryName != "" {
		query = query.Where("category_name LIKE ?", "%"+categoryName+"%")
	}
	err := query.Count(&count).Error
	return count, err
}

// SearchCategories searches for categories by code, type, and name.
func SearchCategories(categoryCode, categoryType, categoryName string, pageSize, pageNum int) ([]models.Category, int64, error) {
	var categories []models.Category
	var count int64

	query := DB.Model(&models.Category{})
	if categoryCode != "" {
		query = query.Where("category_code LIKE ?", categoryCode+"%")
	}
	if categoryType != "" {
		query = query.Where("industry_type LIKE ?", "%"+categoryType+"%")
	}
	if categoryName != "" {
		query = query.Where("category_name LIKE ?", "%"+categoryName+"%")
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Limit(pageSize).Offset((pageNum - 1) * pageSize).Find(&categories).Error
	return categories, count, err
}

func seedData() error {
	// 初始化角色
	roles := []string{"管理员", "普通员工", "测试账号"}
	for _, roleName := range roles {
		DB.FirstOrCreate(&models.Role{Name: roleName}, models.Role{Name: roleName})
	}

	// 初始化管理员用户
	var admin models.User
	if err := DB.Where("username = ?", "admin").First(&admin).Error; err != nil {
		hashedPassword, _ := bcrypt.GenerateFromPassword([]byte("admin@123"), bcrypt.DefaultCost)
		var adminRole models.Role
		DB.Where("name = ?", "管理员").First(&adminRole)
		admin = models.User{Username: "admin", PasswordHash: string(hashedPassword), RoleID: adminRole.ID}
		DB.Create(&admin)
	}

	// 初始化配置
	configs := map[string]string{
		"baseURL":       "http://58.48.136.183:19090/hbyjbg-api/model/v2/",
		"secretId":      "your_secret_id",
		"secretKey":     "your_secret_key",
		"loginName":     "admin",
		"companyName":   "测试公司",
		"linkman":       "测试填报人",
		"telephone":     "13888888888",
		"leader":        "测试单位负责人",
		"statistician":  "测试统计负责人",
		"report_labels": "3.2,3.5",
	}
	for key, value := range configs {
		DB.FirstOrCreate(&models.Config{Key: key}, models.Config{Key: key, Value: value})
	}

	return nil
}

func AddSubmission(userID uint, reportType, filePath string, startTime, endTime, resultTime time.Time, status, errorMsg string) error {
	submission := models.Submission{
		UserID:     userID,
		ReportType: reportType,
		FilePath:   filePath,
		StartTime:  startTime,
		EndTime:    endTime,
		ResultTime: resultTime,
		Status:     status,
		ErrorMsg:   errorMsg}
	return DB.Create(&submission).Error
}

func BatchAddSubmissions(submissions []models.Submission) error {
	return DB.CreateInBatches(submissions, len(submissions)).Error
}

func GetSubmissionsCount() (int64, error) {
	var count int64
	err := DB.Model(&models.Submission{}).Count(&count).Error
	return count, err
}

func GetSubmissions(page, pageSize int) ([]models.Submission, error) {
	var submissions []models.Submission
	offset := (page - 1) * pageSize
	err := DB.Preload("User").Order("created_at desc").Limit(pageSize).Offset(offset).Find(&submissions).Error
	return submissions, err
}

// ClearCategories clears all data from the categories table.
func ClearCategories() error {
	return DB.Exec("DELETE FROM categories").Error
}

func GetUsers(username string, pageSize, pageNum int) ([]models.User, int64, error) {
	var users []models.User
	var count int64

	query := DB.Model(&models.User{})
	if username != "" {
		query = query.Where("username LIKE ?", "%"+username+"%")
	}

	err := query.Count(&count).Error
	if err != nil {
		return nil, 0, err
	}

	err = query.Preload("Role").Limit(pageSize).Offset((pageNum - 1) * pageSize).Find(&users).Error
	return users, count, err
}

// AddUser 添加用户
func AddUser(username, password, roleName string) error {
	var role models.Role
	if err := DB.Where("name = ?", roleName).First(&role).Error; err != nil {
		return fmt.Errorf("查询角色失败: %w", err)
	}

	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("生成密码哈希失败: %w", err)
	}

	user := models.User{Username: username, PasswordHash: string(hashedPassword), RoleID: role.ID}
	return DB.Create(&user).Error
}

// DeleteUser 删除用户
func DeleteUser(userID int) error {
	return DB.Where("id = ?", userID).Delete(&models.User{}).Error
}

func GetUserRole(roleID int) (string, error) {
	var role models.Role
	err := DB.Where("id =?", roleID).First(&role).Error
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("获取用户角色失败")
		return "", fmt.Errorf("获取用户角色失败")
	}
	return role.Name, nil
}

// AddReportCategoryAssociation 添加上报类型与品类的关联
func AddReportCategoryAssociation(reportType, categoryCode string) error {
	association := models.ReportCategoryAssociation{ReportType: reportType, CategoryCode: categoryCode}
	return DB.Create(&association).Error
}

// RemoveReportCategoryAssociation 移除上报类型与品类的关联
func RemoveReportCategoryAssociation(reportType, categoryCode string) error {
	return DB.Where("report_type = ? AND category_code = ?", reportType, categoryCode).Delete(&models.ReportCategoryAssociation{}).Error
}

// GetAssociatedCategories 获取与上报类型关联的品类
func GetAssociatedCategories(reportType string) ([]models.Category, error) {
	var categories []models.Category
	err := DB.Joins("JOIN report_category_associations rca ON categories.category_code = rca.category_code").Where("rca.report_type = ?", reportType).Find(&categories).Error
	return categories, err
}
