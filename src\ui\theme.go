package ui

import (
	"image/color"
	"os"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/theme"
)

// 自定义主题结构体
type MyTheme struct {
	fontPath string
}

// 创建新的自定义主题
func NewMyTheme(fontPath string) *MyTheme {
	return &MyTheme{fontPath: fontPath}
}

// 实现Theme接口的方法
func (m *MyTheme) Color(name fyne.ThemeColorName, variant fyne.ThemeVariant) color.Color {
	switch name {
	case theme.ColorNameDisabled:
		return color.NRGBA{R: 0, G: 0, B: 0, A: 80} // 半透明黑色
	case theme.ColorNameBackground:
		return color.NRGBA{R: 240, G: 240, B: 240, A: 255} // 浅灰色背景
	case theme.ColorNameForeground:
		return color.NRGBA{R: 0, G: 0, B: 0, A: 255} // 黑色文字
	case theme.ColorNameButton:
		return color.NRGBA{R: 220, G: 220, B: 220, A: 255} // 按钮背景色
	case theme.ColorNameInputBackground:
		return color.NRGBA{R: 245, G: 245, B: 245, A: 255} // 输入框背景色
	default:
		return theme.DefaultTheme().Color(name, variant)
	}
}

func (m *MyTheme) Font(style fyne.TextStyle) fyne.Resource {
	if style.Monospace {
		return theme.DefaultTheme().Font(style)
	}

	// 尝试加载微软雅黑字体
	if m.fontPath != "" {
		data, err := os.ReadFile(m.fontPath)
		if err == nil {
			staticFont := &fyne.StaticResource{
				StaticName:    "msyh.ttf",
				StaticContent: data,
			}
			return staticFont
		}
	} else {
		// 尝试加载微软雅黑字体
		fontPath := EnsureMicrosoftYaheiFont()
		data, err := os.ReadFile(fontPath)
		if err == nil {
			staticFont := &fyne.StaticResource{
				StaticName:    "msyh.ttf",
				StaticContent: data,
			}
			return staticFont
		}
	}

	// 如果加载失败，使用默认字体
	return theme.DefaultTheme().Font(style)
}

func (m *MyTheme) Icon(name fyne.ThemeIconName) fyne.Resource {
	return theme.DefaultTheme().Icon(name)
}

func (m *MyTheme) Size(name fyne.ThemeSizeName) float32 {
	return theme.DefaultTheme().Size(name)
}
