package ui

import (
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"simple_inventory_management_system/api"
	"simple_inventory_management_system/internal/auth"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/widget"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"github.com/sirupsen/logrus"
)

type App struct {
	app         fyne.App
	window      fyne.Window
	currentUser *auth.User
}

// const PageSize = 10
const (
	PageSize                       = 20
	ERROR_MSG_COLUMN_WIDTH float32 = 500
	DEFAULT_ROW_HEIGHT     float32 = 28
)

func NewApp() *App {
	a := app.New()

	// 设置自定义主题
	fontPath := ensureMicrosoftYaheiFont()
	a.Settings().SetTheme(NewMyTheme(fontPath))
	companyName, err := getConfig("companyName")
	if err != nil {
		companyName = ""
	}
	title := fmt.Sprintf("%s - 数据上报客户端", companyName)
	w := a.NewWindow(title)

	return &App{
		app:    a,
		window: w,
	}
}

func (a *App) Run() {
	a.showLoginScreen()
	a.mainWin.Resize(fyne.NewSize(400, 200))
	a.mainWin.CenterOnScreen() // 关键居中调用
	a.mainWin.ShowAndRun()
}

func (a *App) handleLogin(username, password string) error {
	logger.Log.WithFields(logrus.Fields{
		"username": username,
		"password": "********",
	}).Info("用户尝试登录")

	user, err := auth.AuthenticateUser(username, password)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"username": username,
			"error":    err,
		}).Error("用户登录失败")
		return err
	}
	a.currentUser = user
	logger.Log.WithFields(logrus.Fields{
		"username": username,
		"role":     user.Role,
	}).Info("用户登录成功")
	a.showMainScreen()
	return nil
}

func (a *App) showLoginScreen() {
	usernameEntry := widget.NewEntry()
	usernameEntry.SetPlaceHolder("用户名")
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("密码")

	loginButton := widget.NewButton("登录", func() {
		if err := a.handleLogin(usernameEntry.Text, passwordEntry.Text); err != nil {
			dialog.ShowError(err, a.mainWin)
		}
	})

	content := container.NewVBox(
		widget.NewForm(
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("密码", passwordEntry),
		),
		container.NewCenter(loginButton),
	)

	setupReturnKeySubmit(a.mainWin, usernameEntry, passwordEntry, loginButton)

	a.mainWin.SetContent(content)
}

func setupReturnKeySubmit(win fyne.Window, username, password *widget.Entry, loginBtn *widget.Button) {
	// 为用户名和密码输入框分别设置回车键监听
	username.OnSubmitted = func(text string) {
		// 当在用户名字段按回车，焦点移到密码字段
		win.Canvas().Focus(password)
	}

	password.OnSubmitted = func(text string) {
		// 当在密码字段按回车，触发登录
		loginBtn.OnTapped()
	}

	// 设置全局回车键监听（备用）
	if deskCanvas, ok := win.Canvas().(desktop.Canvas); ok {
		deskCanvas.SetOnKeyDown(func(event *fyne.KeyEvent) {
			if event.Name == fyne.KeyReturn || event.Name == fyne.KeyEnter {
				// 检查当前焦点在哪个字段
				focused := win.Canvas().Focused()
				if focused == username || focused == password {
					loginBtn.OnTapped()
				}
			}
		})
	}
}

func (a *App) showMainScreen() {
	a.safeResizeAndCenter(1024, 768)
	// a.mainWin.Resize(fyne.NewSize(1024, 768))

	tabs := container.NewAppTabs()

	tabs.Append(container.NewTabItem("数据上报", a.createSubmissionTab()))
	tabs.Append(container.NewTabItem("上报历史", a.createHistoryTab()))

	if a.currentUser.Role == "管理员" {
		tabs.Append(container.NewTabItem("配置管理", a.createConfigTab()))
		tabs.Append(container.NewTabItem("行政区划", a.createAdminDivisionsTab()))
		tabs.Append(container.NewTabItem("地区代码", a.createRegionsTab()))
		tabs.Append(container.NewTabItem("品类管理", a.createCategoriesTab()))
	}

	a.mainWin.SetContent(tabs)
}

func (a *App) safeResizeAndCenter(width, height float32) {
	// 立即刷新状态
	a.mainWin.Canvas().Refresh(a.mainWin.Content())

	// 延迟调整以保证UI响应
	go func() {
		a.mainWin.Resize(fyne.NewSize(width, height))
		// a.mainWin.SetFullScreen(true)
		a.mainWin.CenterOnScreen()
	}()
}

func (a *App) createSubmissionTab() fyne.CanvasObject {
	reportTypeSelect := widget.NewSelect([]string{
		"价格日报",
		"库存日报",
		"粮油加工监测日报",
		"物流运输监测日报",
		"末端配送监测日报",
		"节点库存监测日报",
	}, func(s string) {})
	reportTypeSelect.PlaceHolder = "请选择上报类型"

	filePathEntry := widget.NewEntry()
	filePathEntry.SetPlaceHolder("请选择要上传的数据文件路径")

	fileSelectButton := widget.NewButton("选择文件", func() {
		dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil || reader == nil {
				return
			}
			filePathEntry.SetText(reader.URI().Path())
		}, a.mainWin)
	})

	resultLabel := widget.NewLabel("提交结果将显示在这里")
	resultLabel.Wrapping = fyne.TextWrapWord

	submitButton := widget.NewButton("提交", func() {
		startTime := time.Now()
		reportType := reportTypeSelect.Selected
		filePath := filePathEntry.Text

		if reportType == "" || filePath == "" {
			resultLabel.SetText("错误: 请选择上报类型并选择文件。")
			return
		}

		// 1. Save the file to a date-named folder
		today := time.Now().Format("2006-01-02")
		uploadDir := filepath.Join("uploads", today)
		if err := os.MkdirAll(uploadDir, os.ModePerm); err != nil {
			resultLabel.SetText(fmt.Sprintf("创建目录失败: %v", err))
			return
		}
		dstPath := filepath.Join(uploadDir, filepath.Base(filePath))
		if err := copyFile(filePath, dstPath); err != nil {
			msg := fmt.Sprintf("保存文件失败: %v", err)
			resultLabel.SetText(msg)
			logger.Log.WithFields(logrus.Fields{
				"filePath": filePath,
				"dstPath":  dstPath,
				"error":    err,
				"user":     a.currentUser.Username,
			}).Error("文件保存失败")
			return
		}

		// 2. Read config and initialize client
		baseURL, err1 := getConfig("baseURL")
		secretId, err2 := getConfig("secretId")
		secretKey, err3 := getConfig("secretKey")

		if err1 != nil || err2 != nil || err3 != nil {
			msg := "错误: 无法从数据库加载API配置。"
			resultLabel.SetText(msg)
			saveSubmission(a.currentUser.ID, reportType, dstPath, startTime, time.Now(), time.Now(), "failure", msg)
			return
		}

		client := api.NewClient(baseURL, secretId, secretKey)

		fileBytes, err := os.ReadFile(filePath)
		if err != nil {
			msg := fmt.Sprintf("读取文件失败: %v", err)
			resultLabel.SetText(msg)
			saveSubmission(a.currentUser.ID, reportType, dstPath, startTime, time.Now(), time.Now(), "failure", msg)
			return
		}

		var resp *api.CommonResponse
		var apiErr error

		// 3. Parse and submit data
		endTime := time.Now()
		switch reportType {
		case "价格日报":
			var data api.PriceReportRequest
			if err := json.Unmarshal(fileBytes, &data); err != nil {
				apiErr = fmt.Errorf("解析JSON失败: %w", err)
			} else {
				resp, apiErr = client.SubmitPriceReport(data)
			}
		case "库存日报":
			var data api.InventoryReportRequest
			if err := json.Unmarshal(fileBytes, &data); err != nil {
				apiErr = fmt.Errorf("解析JSON失败: %w", err)
			} else {
				resp, apiErr = client.SubmitInventoryReport(data)
			}
		// ...可以为其他报告类型添加更多的case
		default:
			apiErr = fmt.Errorf("未知的报告类型: %s", reportType)
		}
		resultTime := time.Now()

		// 4. Display result and save submission record
		if apiErr != nil {
			msg := fmt.Sprintf("提交失败: %v", apiErr)
			resultLabel.SetText(msg)
			logger.Log.WithFields(logrus.Fields{
				"reportType": reportType,
				"filePath":   filePath,
				"error":      apiErr,
				"user":       a.currentUser.Username,
			}).Error("API提交失败")
			saveSubmission(a.currentUser.ID, reportType, dstPath, startTime, endTime, resultTime, "failure", msg)
		} else {
			msg := fmt.Sprintf("提交成功!\n代码: %s\n消息: %s\n请求ID: %s", resp.Code, resp.Message, resp.Code)
			resultLabel.SetText(msg)
			saveSubmission(a.currentUser.ID, reportType, dstPath, startTime, endTime, resultTime, "success", "")
		}
	})

	return container.NewVBox(
		widget.NewForm(
			widget.NewFormItem("上报类型", reportTypeSelect),
			widget.NewFormItem("数据文件", container.NewBorder(nil, nil, nil, fileSelectButton, filePathEntry)),
		),
		container.NewCenter(submitButton),
		container.NewVScroll(resultLabel),
	)
}

func (a *App) createHistoryTab() fyne.CanvasObject {
	var submissions []database.Submission
	currentPage := 1
	var totalPages int

	table := widget.NewTable(
		func() (int, int) {
			return len(submissions) + 1, 6 // Rows, Cols (ID, Type, Status, File, Time, Error)
		},
		func() fyne.CanvasObject {
			// CreateCell 现在只需要创建一个 Label 模板
			label := widget.NewLabel("")
			// 关键：在这里就开启换行模式
			label.Wrapping = fyne.TextWrapWord
			return label
		},
		func(id widget.TableCellID, o fyne.CanvasObject) {
			// UpdateCell 也变得更简单
			label := o.(*widget.Label) // 直接断言为 Label

			var text string
			if id.Row == 0 { // 表头行
				switch id.Col {
				case 0:
					text = "ID"
				case 1:
					text = "上报类型"
				case 2:
					text = "状态"
				case 3:
					text = "文件路径"
				case 4:
					text = "创建时间"
				case 5:
					text = "错误信息"
				}
			} else if id.Row-1 < len(submissions) { // 数据行
				sub := submissions[id.Row-1]
				switch id.Col {
				case 0:
					text = fmt.Sprintf("%d", sub.ID)
				case 1:
					text = sub.ReportType
				case 2:
					text = sub.Status
				case 3:
					text = sub.FilePath
				case 4:
					text = sub.CreatedAt.Format("2006-01-02 15:04:05")
				case 5:
					text = sub.ErrorMsg.String
				}
			}
			label.SetText(text)
		},
	)

	table.SetColumnWidth(0, 50)
	table.SetColumnWidth(1, 150)
	table.SetColumnWidth(2, 80)
	table.SetColumnWidth(3, 250)
	table.SetColumnWidth(4, 150)
	table.SetColumnWidth(5, 500)
	//表头行设为默认高度
	table.SetRowHeight(0, DEFAULT_ROW_HEIGHT)
	//创建一个临时可计算高度的label
	tempLabel := widget.NewLabel("")
	tempLabel.Wrapping = fyne.TextWrapWord

	pageLabel := widget.NewLabel("")

	var loadSubmissions func()

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadSubmissions()
		}
	})

	nextButton := widget.NewButton("下一页", func() {
		if currentPage < totalPages {
			currentPage++
			loadSubmissions()
		}
	})

	loadSubmissions = func() {
		totalCount, err := database.GetSubmissionsCount()
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		totalPages = (totalCount + PageSize - 1) / PageSize

		subs, err := database.GetSubmissions(currentPage, PageSize)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		submissions = subs

		for i, sub := range submissions {
			rowID := i + 1

			// 将文本放入临时 Label
			tempLabel.SetText(sub.ErrorMsg.String)

			// 使用一个临时容器来强制宽度，以便正确计算换行后的高度
			tempContainer := container.NewStack(tempLabel)
			tempContainer.Resize(fyne.NewSize(ERROR_MSG_COLUMN_WIDTH, 0))

			// 获取在该宽度下所需的最小高度
			requiredHeight := tempLabel.MinSize().Height

			// 确保行高不低于我们的默认值
			if requiredHeight < DEFAULT_ROW_HEIGHT {
				requiredHeight = DEFAULT_ROW_HEIGHT
			}

			// 应用计算出的高度
			table.SetRowHeight(rowID, requiredHeight)
		}

		table.Refresh()

		pageLabel.SetText(fmt.Sprintf("第 %d / %d 页", currentPage, totalPages))
		if currentPage == 1 {
			prevButton.Disabled()
		}
		if currentPage == totalPages {
			nextButton.Disabled()
		}
	}

	// Initial load
	loadSubmissions()

	pagingControls := container.NewHBox(prevButton, pageLabel, nextButton)

	return container.NewBorder(nil, container.NewCenter(pagingControls), nil, nil, table)
}

const pageSize = 10

func (a *App) createConfigTab() fyne.CanvasObject {
	configs := []database.Config{}
	locaConfig := func() {
		cfgs, err := database.GetAllConfigs()
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"user":  a.currentUser.Username,
			}).Error("加载配置文件失败")
		}
		configs = cfgs
	}
	// headerData := []string{"序号", "配置项", "配置值", "操作"}
	// list := widget.NewList(
	// 	func() int {
	// 		return len(configs)
	// 	},
	// 	func() fyne.CanvasObject {
	// 		return container.NewGridWithColumns(4,
	// 			newCopyableLabel(""),
	// 			newCopyableLabel(""),
	// 			newCopyableLabel(""),
	// 			container.NewHBox(widget.NewButton("修改", nil), widget.NewButton("删除", nil)),
	// 		)
	// 	},
	// 	func(i widget.ListItemID, o fyne.CanvasObject) {
	// 		config := configs[i]
	// 		grid := o.(*fyne.Container)
	// 		grid.Objects[0].(*widget.Entry).SetText(fmt.Sprintf("%d", i+1))
	// 		grid.Objects[1].(*widget.Entry).SetText(config.Key)
	// 		grid.Objects[2].(*widget.Entry).SetText(config.Value)
	// 		hbox := grid.Objects[3].(*fyne.Container)
	// 		editBtn := hbox.Objects[0].(*widget.Button)
	// 		deleteBtn := hbox.Objects[1].(*widget.Button)

	// 		editBtn.OnTapped = func() {
	// 			a.showEditConfigDialog(config, func() {
	// 				a.showMainScreen()
	// 			})
	// 		}
	// 		deleteBtn.OnTapped = func() {
	// 			dialog.ShowConfirm("确认删除", fmt.Sprintf("确定要删除配置 '%s' 吗?", config.Key), func(b bool) {
	// 				if b {
	// 					err := database.DeleteConfig(config.Key)
	// 					if err != nil {
	// 						logger.Log.WithFields(logrus.Fields{
	// 							"key":   config.Key,
	// 							"error": err,
	// 							"user":  a.currentUser.Username,
	// 						}).Error("删除配置失败")
	// 						dialog.ShowError(fmt.Errorf("删除配置失败: %v", err), a.mainWin)
	// 					} else {
	// 						dialog.ShowInformation("成功", "配置删除成功", a.mainWin)
	// 						a.showMainScreen()
	// 					}
	// 				}
	// 			}, a.mainWin)
	// 		}
	// 	},
	// )

	table := widget.NewTable(
		// A. 设置表格的尺寸（行、列）
		func() (int, int) {
			return len(configs) + 1, 4 // 假设我们有三列：序号、配置项、配置值、操作
		},
		// B. 创建单元格模板 (CreateCell)
		// 这是核心：模板包含一个 Label 和一个 Entry，我们将切换它们的可见性
		func() fyne.CanvasObject {
			// return container.NewGridWithColumns(4,
			// 	widget.NewLabel("Template Label"),
			// 	widget.NewLabel("Template Label"),
			// 	widget.NewEntry(),
			// 	widget.NewLabel("Template Label"),
			// )
			label := widget.NewLabel("Template Label")
			entry := widget.NewEntry()
			button := widget.NewButton("删除", nil)
			entry.Hide() // 默认隐藏输入框
			button.Hide()
			// 使用 Max 布局将它们叠在一起
			return container.NewStack(label, entry, button)
		},
		// C. 更新单元格内容 (UpdateCell)
		func(id widget.TableCellID, o fyne.CanvasObject) {
			cellContainer := o.(*fyne.Container)
			label := cellContainer.Objects[0].(*widget.Label)
			entry := cellContainer.Objects[1].(*widget.Entry)
			btn := cellContainer.Objects[2].(*widget.Button)
			text := ""
			if id.Row == 0 { // 表头行
				label.Show()
				switch id.Col {
				case 0:
					text = "序号"
				case 1:
					text = "配置项"
				case 2:
					text = "配置值"
				case 3:
					text = "操作项"
				}
				label.SetText(text)
				entry.Hide()
				btn.Hide()
			} else {
				sub := configs[id.Row-1]
				label.Show()
				switch id.Col {
				case 0:
					entry.Hide()
					btn.Hide()
					text = fmt.Sprintf("%d", id.Row)
					label.SetText(text)
				case 1:
					entry.Hide()
					btn.Hide()
					text = sub.Key
					label.SetText(text)
				case 2:
					label.Hide()
					entry.Show()
					btn.Hide()
					entry.SetText(sub.Value)
					entry.Disable()
				case 3:
					label.Hide()
					entry.Hide()
					btn.Show()
					// editBtn.OnTapped = func() {
					// 	a.showEditConfigDialog(sub, func() {
					// 		a.showMainScreen()
					// 	})
					// }
					btn.OnTapped = func() {
						dialog.ShowConfirm("确认删除", fmt.Sprintf("确定要删除配置 '%s' 吗?", sub.Key), func(b bool) {
							if b {
								err := database.DeleteConfig(sub.Key)
								if err != nil {
									logger.Log.WithFields(logrus.Fields{
										"key":   sub.Key,
										"error": err,
										"user":  a.currentUser.Username,
									}).Error("删除配置失败")
									dialog.ShowError(fmt.Errorf("删除配置失败: %v", err), a.mainWin)
								} else {
									dialog.ShowInformation("成功", "配置删除成功", a.mainWin)
									locaConfig()
								}
							}
						}, a.mainWin)
					}
				}
			}
		},
	)

	// 3. 设置列宽，让表格更好看
	table.SetColumnWidth(0, 80)  // 第一列
	table.SetColumnWidth(1, 150) // 第二列
	table.SetColumnWidth(2, 550) // 第三列（可编辑）

	locaConfig()

	addButton := widget.NewButton("添加配置", func() {
		a.showAddConfigDialog(func() {
			locaConfig()
			table.Refresh()
			// a.showMainScreen() // Refresh main screen to update config tab
		})
	})

	bottomBar := container.NewHBox(layout.NewSpacer(), addButton)
	table.Refresh()
	return container.NewBorder(nil, container.NewCenter(bottomBar), nil, nil, table)
	// content := container.NewBorder(header, bottomBar, nil, nil, list)
	// return table
}

func (a *App) createAdminDivisionsTab() fyne.CanvasObject {
	unitNameEntry := widget.NewEntry()
	unitNameEntry.SetPlaceHolder("按单位名称搜索")

	var divisions []api.AdminDivision
	currentPage := 1
	var totalPages int

	list := widget.NewList(
		func() int {
			return len(divisions)
		},
		func() fyne.CanvasObject {
			return container.NewGridWithColumns(3,
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
			)
		},
		func(i widget.ListItemID, o fyne.CanvasObject) {
			division := divisions[i]
			grid := o.(*fyne.Container)
			grid.Objects[0].(*widget.Entry).SetText(fmt.Sprintf("%d", division.ID))
			grid.Objects[1].(*widget.Entry).SetText(division.Code)
			grid.Objects[2].(*widget.Entry).SetText(division.UnitName)
		},
	)

	pageLabel := widget.NewLabel("")
	var loadData func()

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadData()
		}
	})

	nextButton := widget.NewButton("下一页", func() {
		if currentPage < totalPages {
			currentPage++
			loadData()
		}
	})

	loadData = func() {
		results, total, err := database.SearchAdminDivisions(unitNameEntry.Text, PageSize, currentPage)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		divisions = results
		totalPages = (total + PageSize - 1) / PageSize
		list.Refresh()

		pageLabel.SetText(fmt.Sprintf("第 %d / %d 页", currentPage, totalPages))
		prevButton.Enable()
		nextButton.Enable()
		if currentPage == 1 {
			prevButton.Disable()
		}
		if currentPage >= totalPages {
			nextButton.Disable()
		}
	}

	searchButton := widget.NewButton("搜索", func() {
		currentPage = 1
		loadData()
	})

	importButton := widget.NewButton("导入", func() {
		a.showImportDialog("admin_divisions", func() {
			currentPage = 1
			loadData()
		})
	})

	clearButton := widget.NewButton("清空数据", func() {
		dialog.ShowConfirm("确认清空", "确定要清空所有行政区划数据吗？此操作不可恢复！", func(confirmed bool) {
			if confirmed {
				err := database.ClearAdminDivisions()
				if err != nil {
					logger.Log.WithFields(logrus.Fields{
						"error": err,
						"user":  a.currentUser.Username,
					}).Error("清空行政区划数据失败")
					dialog.ShowError(fmt.Errorf("清空数据失败: %v", err), a.mainWin)
				} else {
					dialog.ShowInformation("成功", "行政区划数据已清空", a.mainWin)
					currentPage = 1
					loadData()
				}
			}
		}, a.mainWin)
	})

	header := container.NewGridWithColumns(3,
		widget.NewLabelWithStyle("ID", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("代码", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("单位名称", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
	)

	searchBar := container.NewBorder(nil, nil, nil, container.NewHBox(searchButton, importButton, clearButton), unitNameEntry)
	pagingControls := container.NewHBox(prevButton, pageLabel, nextButton)

	content := container.NewBorder(container.NewVBox(searchBar, header), container.NewCenter(pagingControls), nil, nil, list)

	loadData() // Initial load

	return content
}

func (a *App) createRegionsTab() fyne.CanvasObject {
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("按名称搜索")

	var regions []api.Region
	currentPage := 1
	var totalPages int

	list := widget.NewList(
		func() int {
			return len(regions)
		},
		func() fyne.CanvasObject {
			return container.NewGridWithColumns(3,
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
			)
		},
		func(i widget.ListItemID, o fyne.CanvasObject) {
			region := regions[i]
			grid := o.(*fyne.Container)
			grid.Objects[0].(*widget.Entry).SetText(fmt.Sprintf("%d", region.ID))
			grid.Objects[1].(*widget.Entry).SetText(region.Code)
			grid.Objects[2].(*widget.Entry).SetText(region.Name)
		},
	)

	pageLabel := widget.NewLabel("")
	var loadData func()

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadData()
		}
	})

	nextButton := widget.NewButton("下一页", func() {
		if currentPage < totalPages {
			currentPage++
			loadData()
		}
	})

	loadData = func() {
		results, total, err := database.SearchRegions(nameEntry.Text, PageSize, currentPage)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		regions = results
		totalPages = (total + PageSize - 1) / PageSize
		list.Refresh()

		pageLabel.SetText(fmt.Sprintf("第 %d / %d 页", currentPage, totalPages))
		prevButton.Enable()
		nextButton.Enable()
		if currentPage == 1 {
			prevButton.Disable()
		}
		if currentPage >= totalPages {
			nextButton.Disable()
		}
	}

	searchButton := widget.NewButton("搜索", func() {
		currentPage = 1
		loadData()
	})

	importButton := widget.NewButton("导入", func() {
		a.showImportDialog("regions", func() {
			currentPage = 1
			loadData()
		})
	})

	clearButton := widget.NewButton("清空数据", func() {
		dialog.ShowConfirm("确认清空", "确定要清空所有地区代码数据吗？此操作不可恢复！", func(confirmed bool) {
			if confirmed {
				err := database.ClearRegions()
				if err != nil {
					logger.Log.WithFields(logrus.Fields{
						"error": err,
						"user":  a.currentUser.Username,
					}).Error("清空地区代码数据失败")
					dialog.ShowError(fmt.Errorf("清空数据失败: %v", err), a.mainWin)
				} else {
					dialog.ShowInformation("成功", "地区代码数据已清空", a.mainWin)
					currentPage = 1
					loadData()
				}
			}
		}, a.mainWin)
	})

	header := container.NewGridWithColumns(3,
		widget.NewLabelWithStyle("ID", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("代码", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("名称", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
	)

	searchBar := container.NewBorder(nil, nil, nil, container.NewHBox(searchButton, importButton, clearButton), nameEntry)
	pagingControls := container.NewHBox(prevButton, pageLabel, nextButton)

	content := container.NewBorder(container.NewVBox(searchBar, header), container.NewCenter(pagingControls), nil, nil, list)

	loadData() // Initial load

	return content
}

func (a *App) createCategoriesTab() fyne.CanvasObject {
	codeEntry := widget.NewEntry()
	codeEntry.SetPlaceHolder("按品类编码搜索")
	typeEntry := widget.NewEntry()
	typeEntry.SetPlaceHolder("按行业类型搜索")
	nameEntry := widget.NewEntry()
	nameEntry.SetPlaceHolder("按品类名称搜索")

	var categories []api.Category
	currentPage := 1
	var totalPages int

	list := widget.NewList(
		func() int {
			return len(categories)
		},
		func() fyne.CanvasObject {
			return container.NewGridWithColumns(7,
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
				newCopyableLabel(""),
			)
		},
		func(i widget.ListItemID, o fyne.CanvasObject) {
			category := categories[i]
			grid := o.(*fyne.Container)
			grid.Objects[0].(*widget.Entry).SetText(fmt.Sprintf("%d", category.ID))
			grid.Objects[1].(*widget.Entry).SetText(category.CategoryCode)
			grid.Objects[2].(*widget.Entry).SetText(category.CategoryName)
			grid.Objects[3].(*widget.Entry).SetText(category.ParentCode)
			grid.Objects[4].(*widget.Entry).SetText(category.IndustryType)
			grid.Objects[5].(*widget.Entry).SetText(category.PriceUnit)
			grid.Objects[6].(*widget.Entry).SetText(category.QuantityUnit)
		},
	)

	pageLabel := widget.NewLabel("")
	var loadData func()

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadData()
		}
	})

	nextButton := widget.NewButton("下一页", func() {
		if currentPage < totalPages {
			currentPage++
			loadData()
		}
	})

	loadData = func() {
		results, total, err := database.SearchCategories(codeEntry.Text, typeEntry.Text, nameEntry.Text, PageSize, currentPage)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		categories = results
		totalPages = (total + PageSize - 1) / PageSize
		list.Refresh()

		pageLabel.SetText(fmt.Sprintf("第 %d / %d 页", currentPage, totalPages))
		prevButton.Enable()
		nextButton.Enable()
		if currentPage == 1 {
			prevButton.Disable()
		}
		if currentPage >= totalPages {
			nextButton.Disable()
		}
	}

	searchButton := widget.NewButton("搜索", func() {
		currentPage = 1
		loadData()
	})

	importButton := widget.NewButton("导入", func() {
		a.showImportDialog("categories", func() {
			currentPage = 1
			loadData()
		})
	})

	header := container.NewGridWithColumns(7,
		widget.NewLabelWithStyle("ID", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("品类编码", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("品类名称", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("父类编码", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("行业类型", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("价格单位", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
		widget.NewLabelWithStyle("数量单位", fyne.TextAlignCenter, fyne.TextStyle{Bold: true}),
	)

	clearButton := widget.NewButton("清空数据", func() {
		dialog.ShowConfirm("确认清空", "确定要清空所有品类数据吗？此操作不可恢复！", func(confirmed bool) {
			if confirmed {
				err := database.ClearCategories()
				if err != nil {
					logger.Log.WithFields(logrus.Fields{
						"error": err,
						"user":  a.currentUser.Username,
					}).Error("清空品类数据失败")
					dialog.ShowError(fmt.Errorf("清空数据失败: %v", err), a.mainWin)
				} else {
					dialog.ShowInformation("成功", "品类数据已清空", a.mainWin)
					currentPage = 1
					loadData()
				}
			}
		}, a.mainWin)
	})

	searchBar := container.NewVBox(
		container.NewGridWithColumns(3, codeEntry, typeEntry, nameEntry),
		container.NewHBox(layout.NewSpacer(), searchButton, importButton, clearButton),
	)
	pagingControls := container.NewHBox(prevButton, pageLabel, nextButton)

	content := container.NewBorder(container.NewVBox(searchBar, header), container.NewCenter(pagingControls), nil, nil, list)

	loadData() // Initial load

	return content
}

func (a *App) showEditConfigDialog(config database.Config, onComplete func()) {
	keyEntry := widget.NewEntry()
	keyEntry.SetText(config.Key)
	keyEntry.Disable()
	valueEntry := widget.NewEntry()
	valueEntry.SetText(config.Value)

	items := []*widget.FormItem{
		widget.NewFormItem("Key", keyEntry),
		widget.NewFormItem("Value", valueEntry),
	}

	dialog.ShowForm("编辑配置", "保存", "取消", items, func(b bool) {
		if b {
			if err := database.UpdateConfig(config.Key, valueEntry.Text); err != nil {
				logger.Log.WithFields(logrus.Fields{
					"key":      config.Key,
					"newValue": valueEntry.Text,
					"error":    err,
					"user":     a.currentUser.Username,
				}).Error("更新配置失败")
				dialog.ShowError(err, a.mainWin)
			} else {
				onComplete()
			}
		}
	}, a.mainWin)
}

func (a *App) showAddConfigDialog(onComplete func()) {
	keyEntry := widget.NewEntry()
	valueEntry := widget.NewEntry()
	valueEntry.MultiLine = true
	items := []*widget.FormItem{
		widget.NewFormItem("Key", keyEntry),
		widget.NewFormItem("Value", valueEntry),
	}

	d := dialog.NewForm("添加新配置", "添加", "取消", items, func(b bool) {
		if b {
			if err := database.AddConfig(keyEntry.Text, valueEntry.Text); err != nil {
				logger.Log.WithFields(logrus.Fields{
					"key":   keyEntry.Text,
					"value": valueEntry.Text,
					"error": err,
					"user":  a.currentUser.Username,
				}).Error("添加配置失败")
				dialog.ShowError(err, a.mainWin)
			} else {
				onComplete()
			}
		}
	}, a.mainWin)
	d.Resize(fyne.NewSize(400, 200))
	d.Show()
}

func getConfig(key string) (string, error) {
	var value string
	err := database.DB.QueryRow("SELECT value FROM configs WHERE key = ?", key).Scan(&value)
	if err != nil {
		if err == sql.ErrNoRows {
			return "", fmt.Errorf("配置项 '%s' 未找到", key)
		}
		return "", err
	}
	return value, nil
}

func saveSubmission(userID int, reportType, filePath string, startTime, endTime, resultTime time.Time, status, errorMsg string) {
	stmt, err := database.DB.Prepare(`INSERT INTO submissions(user_id, report_type, file_path, start_time, end_time, result_time, status, error_msg) VALUES(?, ?, ?, ?, ?, ?, ?, ?)`)
	if err != nil {
		fmt.Printf("数据库预处理语句失败: %v\n", err) // Log to console
		return
	}
	defer stmt.Close()

	_, err = stmt.Exec(userID, reportType, filePath, startTime, endTime, resultTime, status, errorMsg)
	if err != nil {
		fmt.Printf("保存提交记录失败: %v\n", err) // Log to console
	}
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destinationFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destinationFile.Close()

	_, err = io.Copy(destinationFile, sourceFile)
	return err
}

// showImportDialog 显示导入对话框
func (a *App) showImportDialog(tableType string, onComplete func()) {
	filePathEntry := widget.NewEntry()
	filePathEntry.Disable()

	fileSelectButton := widget.NewButton("选择文件", func() {
		fd := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil {
				dialog.ShowError(err, a.mainWin)
				return
			}
			if reader == nil {
				return
			}
			filePathEntry.SetText(reader.URI().Path())
		}, a.mainWin)
		fd.SetFilter(storage.NewExtensionFileFilter([]string{".csv"}))
		fd.Show()
	})

	resultLabel := widget.NewLabel("")
	resultLabel.Wrapping = fyne.TextWrapWord

	items := []*widget.FormItem{
		widget.NewFormItem("CSV文件", container.NewBorder(nil, nil, nil, fileSelectButton, filePathEntry)),
	}

	var title string
	switch tableType {
	case "admin_divisions":
		title = "导入行政区划数据"
	case "regions":
		title = "导入地区代码数据"
	case "categories":
		title = "导入品类数据"
	default:
		title = "导入数据"
	}

	d := dialog.NewForm(title, "导入", "取消", items, func(b bool) {
		if !b {
			return
		}

		filePath := filePathEntry.Text
		if filePath == "" {
			dialog.ShowInformation("提示", "请先选择CSV文件", a.mainWin)
			return
		}

		go func() {
			var err error
			var importCount int

			switch tableType {
			case "admin_divisions":
				importCount, err = a.importAdminDivisions(filePath)
			case "regions":
				importCount, err = a.importRegions(filePath)
			case "categories":
				importCount, err = a.importCategories(filePath)
			default:
				err = fmt.Errorf("未知的表类型: %s", tableType)
			}

			fyne.CurrentApp().SendNotification(&fyne.Notification{
				Title:   "导入结果",
				Content: fmt.Sprintf("导入完成，共导入 %d 条记录", importCount),
			})

			if err != nil {
				fyne.CurrentApp().SendNotification(&fyne.Notification{
					Title:   "导入错误",
					Content: err.Error(),
				})
				logger.Log.WithFields(logrus.Fields{
					"tableType": tableType,
					"filePath":  filePath,
					"error":     err,
					"user":      a.currentUser.Username,
				}).Error("导入数据失败")
			} else {
				onComplete()
			}
		}()

		dialog.ShowInformation("提示", "导入已开始，请稍候...", a.mainWin)
	}, a.mainWin)
	d.Resize(fyne.NewSize(400, 200))
	d.Show()
}

// 	resultLabel := widget.NewLabel("")
// 	resultLabel.Wrapping = fyne.TextWrapWord

// 	// 创建导入按钮并赋值给变量
// 	importButton := widget.NewButton("导入", func() {
// 		filePath := filePathEntry.Text
// 		if filePath == "" {
// 			dialog.ShowInformation("提示", "请先选择CSV文件", a.mainWin)
// 			return
// 		}

// 		go func() {
// 			var err error
// 			var importCount int

// 			switch tableType {
// 			case "admin_divisions":
// 				importCount, err = a.importAdminDivisions(filePath)
// 			case "regions":
// 				importCount, err = a.importRegions(filePath)
// 			case "categories":
// 				importCount, err = a.importCategories(filePath)
// 			default:
// 				err = fmt.Errorf("未知的表类型: %s", tableType)
// 			}

// 			fyne.CurrentApp().SendNotification(&fyne.Notification{
// 				Title:   "导入结果",
// 				Content: fmt.Sprintf("导入完成，共导入 %d 条记录", importCount),
// 			})

// 			if err != nil {
// 				fyne.CurrentApp().SendNotification(&fyne.Notification{
// 					Title:   "导入错误",
// 					Content: err.Error(),
// 				})
// 				logger.Log.WithFields(logrus.Fields{
// 					"tableType": tableType,
// 					"filePath":  filePath,
// 					"error":     err,
// 					"user":      a.currentUser.Username,
// 				}).Error("导入数据失败")
// 			} else {
// 				onComplete()
// 			}
// 		}()

// 		dialog.ShowInformation("提示", "导入已开始，请稍候...", a.mainWin)
// 	})

// 	items := []*widget.FormItem{
// 		widget.NewFormItem("CSV文件", container.NewBorder(nil, nil, nil, fileSelectButton, filePathEntry)),
// 	}

// 	var title string
// 	switch tableType {
// 	case "admin_divisions":
// 		title = "导入行政区划数据"
// 	case "regions":
// 		title = "导入地区代码数据"
// 	case "categories":
// 		title = "导入品类数据"
// 	default:
// 		title = "导入数据"
// 	}

// 	dialog.ShowForm(title, "导入", "取消", items, func(b bool) {
// 		if !b {
// 			return
// 		}
// 		// 导入按钮的点击事件已经处理导入逻辑
// 	}, a.mainWin)
// }

// importAdminDivisions 导入行政区划数据
func (a *App) importAdminDivisions(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(transform.NewReader(file, simplifiedchinese.GBK.NewDecoder()))
	// 跳过标题行
	_, err = reader.Read()
	if err != nil {
		return 0, fmt.Errorf("读取CSV标题行失败: %w", err)
	}

	tx, err := database.DB.Begin()
	if err != nil {
		return 0, fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	stmt, err := tx.Prepare("INSERT INTO admin_divisions (code, unit_name, is_deleted) VALUES (?, ?, ?) ON CONFLICT(code) DO UPDATE SET unit_name = ?, is_deleted = ?")
	if err != nil {
		return 0, fmt.Errorf("准备SQL语句失败: %w", err)
	}
	defer stmt.Close()

	count := 0
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return count, fmt.Errorf("读取CSV记录失败: %w", err)
		}

		if len(record) < 3 {
			continue // 跳过格式不正确的行
		}

		code := enhancedTrim(record[0])
		unitName := enhancedTrim(record[1])
		isDeletedStr := enhancedTrim(record[2])
		isDeleted := 0

		if isDeletedStr == "1" || strings.ToLower(isDeletedStr) == "true" {
			isDeleted = 1
		}

		_, err = stmt.Exec(code, unitName, isDeleted, unitName, isDeleted)
		if err != nil {
			return count, fmt.Errorf("插入数据失败: %w", err)
		}
		count++
	}

	err = tx.Commit()
	if err != nil {
		return count, fmt.Errorf("提交事务失败: %w", err)
	}

	return count, nil
}

// importRegions 导入地区代码数据
func (a *App) importRegions(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(transform.NewReader(file, simplifiedchinese.GBK.NewDecoder()))
	// 跳过标题行
	_, err = reader.Read()
	if err != nil {
		return 0, fmt.Errorf("读取CSV标题行失败: %w", err)
	}

	tx, err := database.DB.Begin()
	if err != nil {
		return 0, fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	stmt, err := tx.Prepare("INSERT INTO regions (code, name, is_deleted) VALUES (?, ?, ?) ON CONFLICT(code) DO UPDATE SET name = ?, is_deleted = ?")
	if err != nil {
		return 0, fmt.Errorf("准备SQL语句失败: %w", err)
	}
	defer stmt.Close()

	count := 0
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return count, fmt.Errorf("读取CSV记录失败: %w", err)
		}

		if len(record) < 3 {
			continue // 跳过格式不正确的行
		}

		code := enhancedTrim(record[0])
		name := enhancedTrim(record[1])
		isDeletedStr := enhancedTrim(record[2])
		isDeleted := 0

		if isDeletedStr == "1" || strings.ToLower(isDeletedStr) == "true" {
			isDeleted = 1
		}

		_, err = stmt.Exec(code, name, isDeleted, name, isDeleted)
		if err != nil {
			return count, fmt.Errorf("插入数据失败: %w", err)
		}
		count++
	}

	err = tx.Commit()
	if err != nil {
		return count, fmt.Errorf("提交事务失败: %w", err)
	}

	return count, nil
}

// importCategories 导入品类数据
func (a *App) importCategories(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(transform.NewReader(file, simplifiedchinese.GBK.NewDecoder()))
	// 跳过标题行
	_, err = reader.Read()
	if err != nil {
		return 0, fmt.Errorf("读取CSV标题行失败: %w", err)
	}

	tx, err := database.DB.Begin()
	if err != nil {
		return 0, fmt.Errorf("开始事务失败: %w", err)
	}
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()

	stmt, err := tx.Prepare("INSERT INTO categories (category_code, category_name, parent_code, industry_type, price_unit, quantity_unit) VALUES (?, ?, ?, ?, ?, ?) ON CONFLICT(category_code) DO UPDATE SET category_name = ?, parent_code = ?, industry_type = ?, price_unit = ?, quantity_unit = ?")
	if err != nil {
		return 0, fmt.Errorf("准备SQL语句失败: %w", err)
	}
	defer stmt.Close()

	count := 0
	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return count, fmt.Errorf("读取CSV记录失败: %w", err)
		}

		if len(record) < 6 {
			continue // 跳过格式不正确的行
		}

		categoryCode := enhancedTrim(record[0])
		categoryName := enhancedTrim(record[1])
		parentCode := enhancedTrim(record[2])
		industryType := enhancedTrim(record[3])
		priceUnit := enhancedTrim(record[4])
		quantityUnit := enhancedTrim(record[5])

		_, err = stmt.Exec(
			categoryCode, categoryName, parentCode, industryType, priceUnit, quantityUnit,
			categoryName, parentCode, industryType, priceUnit, quantityUnit,
		)
		if err != nil {
			return count, fmt.Errorf("插入数据失败: %w", err)
		}
		count++
	}

	err = tx.Commit()
	if err != nil {
		return count, fmt.Errorf("提交事务失败: %w", err)
	}

	return count, nil
}

// newCopyableLabel 创建一个可复制的文本标签
func newCopyableLabel(text string) fyne.CanvasObject {
	entry := widget.NewEntry()
	entry.SetText(text)
	entry.OnChanged = func(string) {} // 防止文本变化
	// 设置样式使其看起来像标签
	entry.TextStyle = fyne.TextStyle{}
	entry.Theme()
	entry.ExtendBaseWidget(entry) // 确保可扩展
	return entry
}

func ensureMicrosoftYaheiFont() string {
	// 应用程序字体目录
	appFontDir := filepath.Join("fonts")
	os.MkdirAll(appFontDir, os.ModePerm)

	// 目标字体文件路径
	destFontPath := filepath.Join(appFontDir, "msyh.ttf")

	// 检查字体文件是否已存在
	if _, err := os.Stat(destFontPath); err == nil {
		return destFontPath
	}

	// 尝试从Windows系统字体目录复制
	systemFontPath := filepath.Join(os.Getenv("WINDIR"), "Fonts", "msyh.ttf")
	if _, err := os.Stat(systemFontPath); err == nil {
		// 复制字体文件
		if err := copyFile(systemFontPath, destFontPath); err == nil {
			return destFontPath
		}
	}

	// 如果无法从系统目录复制，返回空字符串，将使用默认字体
	return ""
}

func enhancedTrim(s string) string {
	s = strings.TrimSpace(s)
	// 处理BOM头
	if len(s) > 0 && s[0] == byte(0xEF) && len(s) > 2 && s[1] == byte(0xBB) && s[2] == byte(0xBF) {
		s = s[1:]
	}
	// 处理零宽空格和控制字符
	s = strings.Map(func(r rune) rune {
		if r < 32 && r != '\t' && r != '\n' && r != '\r' {
			return -1
		}
		// 处理特定Unicode空格
		if r == '\u200B' || r == '\u200C' || r == '\u200D' || r == '\uFEFF' {
			return -1
		}
		return r
	}, s)
	if strings.Contains(s, "？") || strings.Contains(s, "?") {
		s = strings.ReplaceAll(s, "?", "")
		s = strings.ReplaceAll(s, "？", "")
	}
	return strings.TrimSpace(s)
}
