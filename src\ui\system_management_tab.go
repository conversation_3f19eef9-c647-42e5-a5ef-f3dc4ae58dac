package ui

import (
	"fmt"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/models"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

func (a *App) createSystemManagementTab() fyne.CanvasObject {
	tabs := container.NewAppTabs(
		container.NewTabItem("用户管理", a.createUserManagementTab()),
		container.NewTabItem("报表配置", a.createReportConfigTab()),
	)
	return tabs
}

func (a *App) createUserManagementTab() fyne.CanvasObject {
	var users []models.User

	searchEntry := newFixedWidthEntry(200) //widget.NewEntry()
	searchEntry.SetPlaceHolder("搜索用户...")

	table := widget.NewTable(
		func() (int, int) {
			return len(users) + 1, 5
		},
		func() fyne.CanvasObject {
			return widget.NewLabel("")
		},
		func(id widget.TableCellID, cell fyne.CanvasObject) {
			label := cell.(*widget.Label)
			if id.Row == 0 {
				switch id.Col {
				case 0:
					label.SetText("ID")
				case 1:
					label.SetText("用户名")
				case 2:
					label.SetText("角色")
				case 3:
					label.SetText("创建时间")
				case 4:
					label.SetText("操作")
				}
			} else {
				user := users[id.Row-1]
				switch id.Col {
				case 0:
					label.SetText(fmt.Sprintf("%d", user.ID))
				case 1:
					label.SetText(user.Username)
				case 2:
					label.SetText(user.Role.Name)
				case 3:
					label.SetText(user.CreatedAt.Format("2006-01-02 15:04:05"))
				}
			}
		},
	)

	updateUsers := func(filter string) {
		var err error
		users, _, err = database.GetUsers(filter, PageSize, 0)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		table.Refresh()
	}

	// table.SetCellIDFunc(func(row, col int) widget.TableCellID {
	// 	return widget.TableCellID{Row: row, Col: col}
	// })

	table.UpdateCell = func(id widget.TableCellID, cell fyne.CanvasObject) {
		containerCell := cell.(*fyne.Container)
		if id.Row == 0 {
			label := containerCell.Objects[0].(*widget.Label)
			label.SetText([]string{"ID", "用户名", "角色", "创建时间", "操作"}[id.Col])
			return
		}

		user := users[id.Row-1]
		switch id.Col {
		case 0, 1, 2, 3:
			label := containerCell.Objects[0].(*widget.Label)
			var text string
			switch id.Col {
			case 0:
				text = fmt.Sprintf("%d", user.ID)
			case 1:
				text = user.Username
			case 2:
				text = user.Role.Name
			case 3:
				text = user.CreatedAt.Format("2006-01-02 15:04:05")
			}
			label.SetText(text)
			containerCell.Objects = []fyne.CanvasObject{label}
		case 4:
			if user.Username != "admin" {
				deleteButton := widget.NewButton("删除", func() {
					dialog.ShowConfirm("确认删除", fmt.Sprintf("确定要删除用户 '%s'吗?", user.Username), func(confirm bool) {
						if confirm {
							err := database.DeleteUser(int(user.ID))
							if err != nil {
								dialog.ShowError(err, a.mainWin)
							} else {
								dialog.ShowInformation("成功", "用户删除成功", a.mainWin)
								updateUsers(searchEntry.Text)
							}
						}
					}, a.mainWin)
				})
				containerCell.Objects = []fyne.CanvasObject{deleteButton}
			} else {
				containerCell.Objects = []fyne.CanvasObject{widget.NewLabel("")}
			}
		}
		containerCell.Refresh()
	}

	// 设置列宽
	table.SetColumnWidth(0, 60)  // 序号ID
	table.SetColumnWidth(1, 150) // 用户名
	table.SetColumnWidth(2, 200) // 角色
	table.SetColumnWidth(3, 200) // 创建时间
	table.SetColumnWidth(4, 150) // 操作

	table.CreateCell = func() fyne.CanvasObject {
		return container.NewStack(widget.NewLabel(""))
	}

	searchEntry.OnChanged = func(text string) {
		updateUsers(text)
	}

	addButton := widget.NewButton("添加用户", func() {
		a.showAddUserDialog(func() {
			updateUsers("")
		})
	})

	toolbar := container.NewHBox(searchEntry, addButton)
	content := container.NewBorder(toolbar, nil, nil, nil, table)

	updateUsers("")

	return content
}

func (a *App) showAddUserDialog(onSuccess func()) {
	usernameEntry := newFixedWidthEntry(200) //widget.NewEntry()
	usernameEntry.SetPlaceHolder("用户名")
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("密码")
	roleSelect := widget.NewSelect([]string{"管理员", "普通员工", "测试账号"}, nil)

	form := []*widget.FormItem{
		widget.NewFormItem("用户名", usernameEntry),
		widget.NewFormItem("密码", passwordEntry),
		widget.NewFormItem("角色", roleSelect),
	}

	dialog.ShowForm("添加新用户", "添加", "取消", form, func(ok bool) {
		if !ok {
			return
		}

		// hashedPassword, err := bcrypt.GenerateFromPassword([]byte(passwordEntry.Text), bcrypt.DefaultCost)
		// if err != nil {
		// 	dialog.ShowError(err, a.mainWin)
		// 	return
		// }

		// user := api.User{
		// 	Username: usernameEntry.Text,
		// 	Password: string(hashedPassword),
		// 	Role:     roleSelect.Selected,
		// }

		err := database.AddUser(usernameEntry.Text, passwordEntry.Text, roleSelect.Selected)

		// err = database.AddUser(&user)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}

		dialog.ShowInformation("成功", "用户添加成功", a.mainWin)
		if onSuccess != nil {
			onSuccess()
		}
	}, a.mainWin)
}

func (a *App) createReportConfigTab() fyne.CanvasObject {
	return widget.NewLabel("报表配置内容")
}
