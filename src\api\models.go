package api

import (
	"encoding/json"
	"time"
)

// CommonResponse is a generic response structure for all API calls.
// It contains the result code and a message.
// 200: success, 400: service exception, 401: signature authentication failed, 403: insufficient permissions, 500: server internal error
type CommonResponse struct {
	Code    json.Number `json:"code"`    // Response code (can be number or string)
	Message string      `json:"message"` // Response message
}

// PriceReportItem represents a single item in a price report.
// This is used for supermarket, wholesale, and farmer's market price data.
// rptdate, loginName, commodityId are a combined unique key.
type PriceReportItem struct {
	LoginName    string `json:"loginName"`    // Enterprise unified platform code
	CommodityID  string `json:"commodityId"`  // Category code
	Price        string `json:"price"`        // Retail price
	RptDate      string `json:"rptdate"`      // Report date in "yyyy-MM-dd" format
	CreateTime   string `json:"createTime"`   // Submission date in "yyyy-MM-dd hh24:mi:ss" format
	UpdateTime   string `json:"updateTime"`   // Modification time in "yyyy-MM-dd hh24:mi:ss" format
	Linkman      string `json:"linkman"`      // Person who filled out the form
	Telephone    string `json:"telephone"`    // Contact number
	Memo         string `json:"memo"`         // Brief analysis
	Leader       string `json:"leader"`       // Head of the unit
	Statistician string `json:"statistician"` // Head of statistics
}

// PriceReportRequest is the request body for submitting price data.
// It contains a list of price report items.
type PriceReportRequest struct {
	Items []PriceReportItem `json:"items"`
}

// InventoryReportItem represents a single item in an inventory report.
// This is used for supermarket and wholesale inventory data.
// rptdate, loginName, commodityId, originPlace are a combined unique key.
type InventoryReportItem struct {
	LoginName           string `json:"loginName"`           // Enterprise unified platform code
	CommodityID         string `json:"commodityId"`         // Category code
	Stock               string `json:"stock"`               // Inventory quantity
	Amount              string `json:"amount"`              // Sales volume
	InStock             string `json:"instock"`             // Purchase quantity
	RptDate             string `json:"rptdate"`             // Report date in "yyyy-MM-dd" format
	CreateTime          string `json:"createTime"`          // Submission date in "yyyy-MM-dd hh24:mi:ss" format
	UpdateTime          string `json:"updateTime"`          // Modification time in "yyyy-MM-dd hh24:mi:ss" format
	Linkman             string `json:"linkman"`             // Person who filled out the form
	Telephone           string `json:"telephone"`           // Contact number
	Memo                string `json:"memo"`                // Brief analysis
	Leader              string `json:"leader"`              // Head of the unit
	Statistician        string `json:"statistician"`        // Head of statistics
	OriginPlace         string `json:"originPlace"`         // Place of origin (6-digit area code)
	ManufacturerName    string `json:"manufacturerName"`    // Manufacturer name
	ManufacturerAddress string `json:"manufacturerAddress"` // Manufacturer address
}

// InventoryReportRequest is the request body for submitting inventory data.
// It contains a list of inventory report items.
type InventoryReportRequest struct {
	Items []InventoryReportItem `json:"items"`
}

// FoodProcessingReportItem represents a single item in a food processing report.
// rptdate, loginName, commodityId are a combined unique key.
type FoodProcessingReportItem struct {
	LoginName       string `json:"loginName"`       // Enterprise unified platform code
	RptDate         string `json:"rptdate"`         // Report date in "yyyy-MM" format
	CommodityID     string `json:"commodityId"`     // Category code
	TotalStore      string `json:"totalStore"`      // Inventory
	TotalProduction string `json:"totalProduction"` // Production
	TotalSales      string `json:"totalSales"`      // Sales
	DayProduction   string `json:"dayProduction"`   // Daily capacity
	TakeOutAmount   string `json:"takeOutAmount"`   // Amount available for transfer
	CreateTime      string `json:"createTime"`      // Submission date in "yyyy-MM-dd hh24:mi:ss" format
	UpdateTime      string `json:"updateTime"`      // Modification time in "yyyy-MM-dd hh24:mi:ss" format
	Linkman         string `json:"linkman"`         // Person who filled out the form
	Telephone       string `json:"telephone"`       // Contact number
	Memo            string `json:"memo"`            // Brief analysis
	Leader          string `json:"leader"`          // Head of the unit
	Statistician    string `json:"statistician"`    // Head of statistics
}

// FoodProcessingReportRequest is the request body for submitting food processing data.
type FoodProcessingReportRequest struct {
	Items []FoodProcessingReportItem `json:"items"`
}

// LogisticsReportItem represents a single item in a logistics transport report.
// deployId, loginName, vehicle, driverMobile, departTime, commodityId are a combined unique key.
type LogisticsReportItem struct {
	DeployID        string `json:"deployId"`        // Dispatch ID
	LoginName       string `json:"loginName"`       // Enterprise unified platform code
	EnterName       string `json:"enterName"`       // Enterprise name
	Vehicle         string `json:"vehicle"`         // License plate number
	Driver          string `json:"driver"`          // Driver's name
	DriverMobile    string `json:"driverMobile"`    // Driver's mobile number
	DepartProvince  string `json:"departProvince"`  // Departure province code
	DepartCity      string `json:"departCity"`      // Departure city code
	DepartCounty    string `json:"departCounty"`    // Departure county code
	DepartPlace     string `json:"departPlace"`     // Detailed departure address
	DepartTime      string `json:"departTime"`      // Departure time in "yyyy-MM-dd hh24:mi:ss" format
	ArrivalProvince string `json:"arrivalProvince"` // Arrival province code
	ArrivalCity     string `json:"arrivalCity"`     // Arrival city code
	ArrivalCounty   string `json:"arrivalCounty"`   // Arrival county code
	ArrivalPlace    string `json:"arrivalPlace"`    // Detailed arrival address
	ArrivalTime     string `json:"arrivalTime"`     // Estimated arrival time in "yyyy-MM-dd hh24:mi:ss" format
	IsArrival       string `json:"isArrival"`       // Whether arrived (1 for yes, 0 for no)
	IsFreeze        string `json:"isFreeze"`        // Whether cold chain (1 for yes, 0 for no)
	CommodityID     string `json:"commodityId"`     // Category code
	Number          string `json:"number"`          // Quantity
	CreateTime      string `json:"createTime"`      // Submission date in "yyyy-MM-dd hh24:mi:ss" format
	UpdateTime      string `json:"updateTime"`      // Modification time in "yyyy-MM-dd hh24:mi:ss" format
	Linkman         string `json:"linkman"`         // Person who filled out the form
	Telephone       string `json:"telephone"`       // Contact number
	Memo            string `json:"memo"`            // Brief analysis
	Leader          string `json:"leader"`          // Head of the unit
	Statistician    string `json:"statistician"`    // Head of statistics
}

// LogisticsReportRequest is the request body for submitting logistics data.
type LogisticsReportRequest struct {
	Items []LogisticsReportItem `json:"items"`
}

// DeliveryReportItem represents a single item in a terminal delivery report.
// deployId, loginName, waybill are a combined unique key.
type DeliveryReportItem struct {
	DeployID        string `json:"deployId"`        // Dispatch ID
	LoginName       string `json:"loginName"`       // Enterprise unified platform code
	EnterName       string `json:"enterName"`       // Enterprise name
	Waybill         string `json:"waybill"`         // Unique delivery waybill code
	Courier         string `json:"courier"`         // Courier's name
	CourierMobile   string `json:"courierMobile"`   // Courier's mobile number
	DepartDepot     string `json:"departDepot"`     // Departure warehouse
	DepartTime      string `json:"departTime"`      // Departure time in "yyyy-MM-dd hh24:mi:ss" format
	ArrivalAreaName string `json:"arrivalAreaName"` // Arrival point name
	ArrivalTime     string `json:"arrivalTime"`     // Arrival time in "yyyy-MM-dd hh24:mi:ss" format
	IsArrival       string `json:"isArrival"`       // Whether arrived (1 for yes, 0 for no)
	CommodityID     string `json:"commodityId"`     // Category code
	CreateTime      string `json:"createTime"`      // Submission date in "yyyy-MM-dd hh24:mi:ss" format
	UpdateTime      string `json:"updateTime"`      // Modification time in "yyyy-MM-dd hh24:mi:ss" format
	Linkman         string `json:"linkman"`         // Person who filled out the form
	Telephone       string `json:"telephone"`       // Contact number
	Memo            string `json:"memo"`            // Brief analysis
	Leader          string `json:"leader"`          // Head of the unit
	Statistician    string `json:"statistician"`    // Head of statistics
}

// DeliveryReportRequest is the request body for submitting delivery data.
type DeliveryReportRequest struct {
	Items []DeliveryReportItem `json:"items"`
}

// NodeInventoryReportItem represents a single item in a node-level inventory report.
// rpdate, nodeCode/foreignId, commodityId are a combined unique key.
type NodeInventoryReportItem struct {
	NodeCode    string `json:"nodeCode,omitempty"`  // Node code
	ForeignID   string `json:"foreignId,omitempty"` // Node external code
	CommodityID string `json:"commodityId"`         // Category code
	Stock       string `json:"stock,omitempty"`     // Inventory quantity
	InStock     string `json:"instock,omitempty"`   // Purchase quantity
	Amount      string `json:"amount,omitempty"`    // Sales volume
	RptDate     string `json:"rpdate"`              // Report date in "yyyy-MM-dd hh:mm:ss" format
}

// NodeInventoryReportRequest is the request body for submitting node-level inventory data.
type NodeInventoryReportRequest struct {
	Items []NodeInventoryReportItem `json:"items"`
}

// Category represents a category configuration item.

// Category represents a category configuration item.

// Category represents a category configuration item.
type Category struct {
	ID           int    `json:"id" gorm:"primaryKey;autoIncrement"` // 自增id
	CategoryType string `json:"CategoryType"`                       //分类 市场监测品类/应急保供零售品类/应急保供批发品类/食品加工品类/物流数据品类
	CategoryCode string `json:"categoryCode"`                       // 品类编码
	CategoryName string `json:"categoryName"`                       // 品类名称
	ParentCode   string `json:"parentCode"`                         // 父类编码
	IndustryType string `json:"industryType"`                       // 行业类型
	PriceUnit    string `json:"priceUnit"`                          // 价格单位
	QuantityUnit string `json:"quantityUnit"`                       // 数量单位
}

// Region represents a region name code item.

type Region struct {
	ID        int    `json:"id" gorm:"primaryKey;autoIncrement"` // 自增id
	Code      string `json:"code"`                               // 代码
	Name      string `json:"name"`                               // 名称
	IsDeleted int    `json:"isDeleted"`                          // 是否删除
}

// AdminDivision represents an administrative division dictionary item.

type AdminDivision struct {
	ID        int    `json:"id" gorm:"primaryKey;autoIncrement"` // 自增id
	Code      string `json:"code"`                               // 行政区划代码
	UnitName  string `json:"unitName"`                           // 单位名称
	IsDeleted int    `json:"isDeleted"`                          // 是否删除
}

type User struct {
	ID        int
	Username  string
	Role      string
	RoleID    int
	CreatedAt time.Time
}
