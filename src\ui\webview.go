package ui

import (
	"simple_inventory_management_system/ai"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
)

// WebView 是一个自定义的WebView组件
type WebView struct {
	widget.BaseWidget
	url     string
	content fyne.CanvasObject
}

// NewWebView 创建一个新的WebView实例
func NewWebView() *WebView {
	w := &WebView{}
	w.ExtendBaseWidget(w)

	// 初始化时显示加载提示
	w.content = container.NewCenter(
		container.NewVBox(
			widget.NewLabel("AI对话助手"),
			widget.NewLabel("正在加载..."),
		),
	)

	return w
}

// LoadURL 加载指定的URL
func (w *WebView) LoadURL(url string) {
	w.url = url

	// 由于Fyne没有内置的WebView支持，我们创建一个简化的界面
	// 在实际应用中，这里可以集成第三方WebView库或使用系统浏览器
	w.content = w.createWebContent()
	w.Refresh()
}

// Reload 重新加载当前页面
func (w *WebView) Reload() {
	if w.url != "" {
		w.LoadURL(w.url)
	}
}

// createWebContent 创建Web内容的替代界面
func (w *WebView) createWebContent() fyne.CanvasObject {
	// 创建一个简化的聊天界面作为WebView的替代
	return w.createSimpleChatInterface()
}

// createSimpleChatInterface 创建简化的聊天界面
func (w *WebView) createSimpleChatInterface() fyne.CanvasObject {
	// 初始化聊天内容
	initialContent := `# AI 对话助手

欢迎使用AI对话助手！

**功能说明：**
- 这是一个内置的AI对话界面
- 您可以在下方输入框中输入问题
- 按回车键或点击发送按钮发送消息

**使用提示：**
- 支持多行输入
- 支持Markdown格式显示
- 对话记录会保存在当前会话中

---

**系统消息：** 您好！我是您的AI助手，有什么可以帮助您的吗？
`

	// 聊天消息显示区域
	chatArea := widget.NewRichTextFromMarkdown(initialContent)
	chatArea.Wrapping = fyne.TextWrapWord
	chatScroll := container.NewScroll(chatArea)
	chatScroll.SetMinSize(fyne.NewSize(400, 300))

	// 用于存储当前聊天内容的变量
	var currentChatContent = initialContent

	// 输入区域
	inputEntry := widget.NewMultiLineEntry()
	inputEntry.SetPlaceHolder("请输入您的问题...")
	inputEntry.Resize(fyne.NewSize(400, 80))

	// 发送按钮
	sendButton := widget.NewButton("发送", func() {
		message := inputEntry.Text
		if message == "" {
			return
		}

		// 添加用户消息到聊天内容
		currentChatContent += "\n\n**您：** " + message

		// 模拟AI回复
		aiResponse := w.generateAIResponse(message)
		currentChatContent += "\n\n**AI助手：** " + aiResponse

		// 更新聊天区域显示
		chatArea.ParseMarkdown(currentChatContent)
		inputEntry.SetText("")

		// 滚动到底部
		chatScroll.ScrollToBottom()
	})

	// 清空按钮
	clearButton := widget.NewButton("清空对话", func() {
		// 重置聊天内容
		currentChatContent = `# AI 对话助手

**系统消息：** 对话已清空。有什么可以帮助您的吗？
`
		chatArea.ParseMarkdown(currentChatContent)
	})

	// 输入区域布局
	inputContainer := container.NewBorder(
		nil,
		container.NewHBox(clearButton, sendButton),
		nil,
		nil,
		inputEntry,
	)

	// 整体布局
	return container.NewBorder(
		nil,            // 顶部
		inputContainer, // 底部：输入区域
		nil,            // 左侧
		nil,            // 右侧
		chatScroll,     // 中心：聊天区域
	)
}

// generateAIResponse 生成AI回复
func (w *WebView) generateAIResponse(message string) string {
	// 从配置中获取 OpenRouter API Key
	apiKey, err := database.GetConfig("openrouter_api_key")
	if err != nil || apiKey == "" {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Warn("未配置 OpenRouter API Key，使用本地回复")
		return w.getLocalResponse(message)
	}

	// 创建 OpenRouter 客户端
	client := ai.NewOpenRouterClient(apiKey)

	// 构建消息历史
	messages := []ai.Message{
		{
			Role:    "system",
			Content: "你是一个专业的库存管理系统助手，请用中文回答用户的问题。你可以帮助用户了解系统功能、解答使用问题、提供技术支持。请保持友好、专业的语调。",
		},
		{
			Role:    "user",
			Content: message,
		},
	}

	// 发送请求到 OpenRouter
	response, err := client.Chat(messages, "")
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":   err,
			"message": message,
		}).Error("OpenRouter API 调用失败")
		return w.getLocalResponse(message) + "\n\n*注意：AI服务暂时不可用，已切换到本地回复*"
	}

	// 检查响应
	if len(response.Choices) == 0 {
		logger.Log.Warn("OpenRouter API 返回空响应")
		return w.getLocalResponse(message) + "\n\n*注意：AI服务返回空响应，已切换到本地回复*"
	}

	// 记录使用的模型
	modelName := client.GetModelName(response.Model)
	logger.Log.WithFields(logrus.Fields{
		"model":      response.Model,
		"model_name": modelName,
		"message":    message,
		"tokens":     response.Usage.TotalTokens,
	}).Info("AI 对话成功")

	// 在回复中添加模型信息
	aiResponse := response.Choices[0].Message.Content
	aiResponse += "\n\n---\n*由 " + modelName + " 提供支持*"

	return aiResponse
}

// getLocalResponse 获取本地预设回复
func (w *WebView) getLocalResponse(message string) string {
	responses := map[string]string{
		"你好": "您好！很高兴为您服务。有什么我可以帮助您的吗？",
		"您好": "您好！很高兴为您服务。有什么我可以帮助您的吗？",
		"谢谢": "不客气！如果您还有其他问题，随时可以问我。",
		"感谢": "不客气！如果您还有其他问题，随时可以问我。",
		"再见": "再见！祝您工作顺利，有需要随时联系我。",
		"拜拜": "再见！祝您工作顺利，有需要随时联系我。",
		"帮助": "我可以帮助您解答各种问题，包括：\n- 系统使用指导\n- 数据上报相关问题\n- 技术支持\n- 其他工作相关咨询",
		"功能": "本系统主要功能包括：\n- 数据上报\n- 历史记录查看\n- 模板下载\n- 系统管理\n- AI对话助手",
		"配置": "要使用AI对话功能，请在配置管理中添加 'openrouter_api_key' 配置项，并设置您的 OpenRouter API 密钥。",
	}

	// 检查是否有预设回复
	for key, response := range responses {
		if message == key {
			return response
		}
	}

	// 默认回复
	return "我理解您的问题。基于您的输入「" + message + "」，我建议您可以：\n\n1. 查看相关的系统功能模块\n2. 参考帮助文档\n3. 如需更详细的帮助，请提供更多具体信息\n\n**提示：** 要启用完整的AI对话功能，请在配置管理中设置 'openrouter_api_key'。"
}

// CreateRenderer 实现fyne.Widget接口
func (w *WebView) CreateRenderer() fyne.WidgetRenderer {
	return &webViewRenderer{
		webView: w,
		content: w.content,
	}
}

// webViewRenderer WebView的渲染器
type webViewRenderer struct {
	webView *WebView
	content fyne.CanvasObject
}

func (r *webViewRenderer) Layout(size fyne.Size) {
	if r.content != nil {
		r.content.Resize(size)
	}
}

func (r *webViewRenderer) MinSize() fyne.Size {
	if r.content != nil {
		return r.content.MinSize()
	}
	return fyne.NewSize(400, 300)
}

func (r *webViewRenderer) Refresh() {
	r.content = r.webView.content
	if r.content != nil {
		r.content.Refresh()
	}
}

func (r *webViewRenderer) Objects() []fyne.CanvasObject {
	if r.content != nil {
		return []fyne.CanvasObject{r.content}
	}
	return []fyne.CanvasObject{}
}

func (r *webViewRenderer) Destroy() {
	// 清理资源
}
