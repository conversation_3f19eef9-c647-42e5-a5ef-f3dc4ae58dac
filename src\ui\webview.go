package ui

import (
	"fmt"
	"simple_inventory_management_system/ai"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
)

// WebView 是一个自定义的WebView组件
type WebView struct {
	widget.BaseWidget
	url            string
	content        fyne.CanvasObject
	window         fyne.Window  // 添加窗口引用用于显示通知
	chatHistory    []ai.Message // 对话历史记录
	maxHistorySize int          // 最大历史记录数量
}

// NewWebView 创建一个新的WebView实例
func NewWebView() *WebView {
	w := &WebView{
		chatHistory:    make([]ai.Message, 0),
		maxHistorySize: 40, // 最大记住20个问答（每个问答包含用户消息和AI回复，共40条消息）
	}
	w.ExtendBaseWidget(w)

	// 初始化时显示加载提示
	w.content = container.NewCenter(
		container.NewVBox(
			widget.NewLabel("AI对话助手"),
			widget.NewLabel("正在加载..."),
		),
	)

	return w
}

// SetWindow 设置窗口引用
func (w *WebView) SetWindow(window fyne.Window) {
	w.window = window
}

// addToHistory 添加消息到历史记录
func (w *WebView) addToHistory(role, content string) {
	message := ai.Message{
		Role:    role,
		Content: content,
	}

	w.chatHistory = append(w.chatHistory, message)

	// 如果超过最大历史记录数量，删除最旧的记录
	if len(w.chatHistory) > w.maxHistorySize {
		// 保留最新的记录，删除最旧的
		w.chatHistory = w.chatHistory[len(w.chatHistory)-w.maxHistorySize:]

		logger.Log.WithFields(logrus.Fields{
			"removed_old_messages": len(w.chatHistory) - w.maxHistorySize,
			"current_count":        len(w.chatHistory),
		}).Info("删除旧的历史记录以保持在限制范围内")
	}

	logger.Log.WithFields(logrus.Fields{
		"history_count": len(w.chatHistory),
		"role":          role,
		"content_len":   len(content),
	}).Debug("添加消息到历史记录")
}

// clearHistory 清空历史记录
func (w *WebView) clearHistory() {
	w.chatHistory = make([]ai.Message, 0)
	logger.Log.Info("对话历史记录已清空")
}

// getHistoryWithSystemPrompt 获取包含系统提示的完整历史记录
func (w *WebView) getHistoryWithSystemPrompt() []ai.Message {
	systemMessage := ai.Message{
		Role:    "system",
		Content: "你是一个专业的库存管理系统助手，请用中文回答用户的问题。你可以帮助用户了解系统功能、解答使用问题、提供技术支持。请保持友好、专业的语调。请记住之前的对话内容，提供连贯的回答。",
	}

	// 将系统消息放在最前面，然后是历史记录
	messages := []ai.Message{systemMessage}
	messages = append(messages, w.chatHistory...)

	return messages
}

// LoadURL 加载指定的URL
func (w *WebView) LoadURL(url string) {
	w.url = url

	// 由于Fyne没有内置的WebView支持，我们创建一个简化的界面
	// 在实际应用中，这里可以集成第三方WebView库或使用系统浏览器
	w.content = w.createWebContent()
	w.Refresh()
}

// Reload 重新加载当前页面
func (w *WebView) Reload() {
	if w.url != "" {
		w.LoadURL(w.url)
	}
}

// createWebContent 创建Web内容的替代界面
func (w *WebView) createWebContent() fyne.CanvasObject {
	// 创建一个简化的聊天界面作为WebView的替代
	return w.createSimpleChatInterface()
}

// createSimpleChatInterface 创建简化的聊天界面
func (w *WebView) createSimpleChatInterface() fyne.CanvasObject {
	// 创建聊天消息容器
	chatContainer := container.NewVBox()

	// 添加欢迎消息
	welcomeMsg := w.createMessageWidget("系统", "您好！我是您的AI助手，有什么可以帮助您的吗？\n\n功能说明：\n- 支持智能模型选择\n- 可复制AI回复内容\n- 支持多轮对话", true)
	chatContainer.Add(welcomeMsg)

	// 创建滚动容器
	chatScroll := container.NewScroll(chatContainer)
	chatScroll.SetMinSize(fyne.NewSize(400, 300))

	// 输入区域
	inputEntry := widget.NewMultiLineEntry()
	inputEntry.SetPlaceHolder("请输入您的问题...")
	inputEntry.Resize(fyne.NewSize(400, 80))

	// 发送按钮
	sendButton := widget.NewButton("发送", func() {
		message := inputEntry.Text
		if message == "" {
			return
		}

		// 添加用户消息到历史记录
		w.addToHistory("user", message)

		// 添加用户消息到聊天容器
		userMsg := w.createMessageWidget("您", message, false)
		chatContainer.Add(userMsg)

		// 清空输入框
		inputEntry.SetText("")

		// 滚动到底部
		chatScroll.ScrollToBottom()

		// 在新的 goroutine 中获取AI回复，避免阻塞UI
		go func() {
			aiResponse := w.generateAIResponse(message)

			// 添加AI回复到历史记录
			w.addToHistory("assistant", aiResponse)

			// 在主线程中更新UI
			go func() {
				aiMsg := w.createMessageWidget("AI助手", aiResponse, false)
				chatContainer.Add(aiMsg)
				chatScroll.ScrollToBottom()
			}()
		}()
	})

	// 清空按钮
	clearButton := widget.NewButton("清空对话", func() {
		// 清空对话历史记录
		w.clearHistory()

		// 清空聊天容器
		chatContainer.Objects = nil

		// 重新添加欢迎消息
		welcomeMsg := w.createMessageWidget("系统", "对话已清空。有什么可以帮助您的吗？", true)
		chatContainer.Add(welcomeMsg)

		// 刷新容器
		chatContainer.Refresh()
	})

	// 创建状态栏
	statusLabel := widget.NewLabel("对话记录: 0 条")
	statusLabel.Alignment = fyne.TextAlignCenter
	statusLabel.Importance = widget.LowImportance

	// 更新状态栏的函数
	updateStatus := func() {
		count := len(w.chatHistory)
		maxPairs := w.maxHistorySize / 2 // 每对问答包含2条消息
		statusLabel.SetText(fmt.Sprintf("对话记录: %d 条 (最多保留 %d 对问答)", count, maxPairs))
	}

	// 初始更新状态
	updateStatus()

	// 输入区域布局
	inputContainer := container.NewBorder(
		nil,
		container.NewHBox(clearButton, sendButton),
		nil,
		nil,
		inputEntry,
	)

	// 整体布局
	return container.NewBorder(
		statusLabel,    // 顶部：状态栏
		inputContainer, // 底部：输入区域
		nil,            // 左侧
		nil,            // 右侧
		chatScroll,     // 中心：聊天区域
	)
}

// generateAIResponse 生成AI回复
func (w *WebView) generateAIResponse(message string) string {
	// 从配置中获取 OpenRouter API Key
	apiKey, err := database.GetConfig("openrouter_api_key")
	if err != nil || apiKey == "" {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Warn("未配置 OpenRouter API Key，使用本地回复")
		return w.getLocalResponse(message)
	}

	// 创建 OpenRouter 客户端
	client := ai.NewOpenRouterClient(apiKey)

	// 获取包含历史记录的完整消息列表
	messages := w.getHistoryWithSystemPrompt()

	// 添加当前用户消息（注意：这里不添加到历史记录，因为在发送按钮中已经添加了）
	currentMessage := ai.Message{
		Role:    "user",
		Content: message,
	}
	messages = append(messages, currentMessage)

	logger.Log.WithFields(logrus.Fields{
		"total_messages": len(messages),
		"history_count":  len(w.chatHistory),
		"current_msg":    message,
	}).Info("发送AI请求，包含历史记录")

	// 尝试多个模型进行重试
	return w.tryMultipleModels(client, messages, message)
}

// tryMultipleModels 尝试多个模型进行重试
func (w *WebView) tryMultipleModels(client *ai.OpenRouterClient, messages []ai.Message, originalMessage string) string {
	// 获取可用模型列表
	models := client.GetAvailableModels()

	// 首先尝试智能选择的模型
	selectedModel := client.SelectModel(originalMessage)
	response, err := w.tryModel(client, messages, selectedModel, originalMessage)
	if err == nil {
		return response
	}

	// 如果智能选择的模型失败，尝试其他模型
	for _, model := range models {
		if model.ID == selectedModel {
			continue // 跳过已经尝试过的模型
		}

		logger.Log.WithFields(logrus.Fields{
			"model":   model.ID,
			"message": originalMessage,
		}).Info("尝试备用模型")

		response, err := w.tryModel(client, messages, model.ID, originalMessage)
		if err == nil {
			return response
		}
	}

	// 所有模型都失败了，返回本地回复
	logger.Log.WithFields(logrus.Fields{
		"message": originalMessage,
	}).Error("所有AI模型都调用失败，使用本地回复")

	return w.getLocalResponse(originalMessage) + "\n\n*注意：所有AI服务都暂时不可用，已切换到本地回复*"
}

// tryModel 尝试单个模型
func (w *WebView) tryModel(client *ai.OpenRouterClient, messages []ai.Message, modelID string, originalMessage string) (string, error) {
	response, err := client.Chat(messages, modelID)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":   err,
			"model":   modelID,
			"message": originalMessage,
		}).Error("模型调用失败")
		return "", err
	}

	// 检查响应
	if len(response.Choices) == 0 {
		logger.Log.WithFields(logrus.Fields{
			"model":   modelID,
			"message": originalMessage,
		}).Warn("模型返回空响应")
		return "", fmt.Errorf("模型返回空响应")
	}

	// 记录使用的模型
	modelName := client.GetModelName(response.Model)
	logger.Log.WithFields(logrus.Fields{
		"model":      response.Model,
		"model_name": modelName,
		"message":    originalMessage,
		"tokens":     response.Usage.TotalTokens,
	}).Info("AI 对话成功")

	// 在回复中添加模型信息
	aiResponse := response.Choices[0].Message.Content
	aiResponse += "\n\n---\n*由 " + modelName + " 提供支持*"

	return aiResponse, nil
}

// createMessageWidget 创建可复制的消息组件
func (w *WebView) createMessageWidget(sender, content string, isSystem bool) fyne.CanvasObject {
	// 创建发送者标签和复制按钮的容器
	var headerContainer *fyne.Container

	// 创建发送者标签
	senderLabel := widget.NewLabelWithStyle(sender+":", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})

	// 根据发送者设置不同的样式
	if isSystem {
		senderLabel.Importance = widget.MediumImportance
	} else if sender == "您" {
		senderLabel.Importance = widget.HighImportance
	} else {
		senderLabel.Importance = widget.LowImportance
	}

	// 创建复制按钮
	copyButton := widget.NewButton("复制", func() {
		w.copyToClipboard(content)
	})
	copyButton.Resize(fyne.NewSize(60, 30))
	copyButton.Importance = widget.LowImportance

	// 创建头部容器（发送者标签 + 复制按钮）
	headerContainer = container.NewBorder(
		nil, nil, // 上下
		copyButton, nil, // 左右：复制按钮在右边
		senderLabel, // 中心：发送者标签
	)

	var contentWidget fyne.CanvasObject

	// 如果是AI助手的回复，尝试解析Markdown
	if sender == "AI助手" && !isSystem {
		// 使用 RichText 组件解析 Markdown
		richText := widget.NewRichTextFromMarkdown(content)
		richText.Wrapping = fyne.TextWrapWord

		// 计算合适的高度
		height := w.calculateRichTextHeight(content)
		richText.Resize(fyne.NewSize(600, height))

		contentWidget = richText
	} else {
		// 对于用户消息和系统消息，使用可复制的文本框
		contentEntry := widget.NewMultiLineEntry()
		contentEntry.SetText(content)
		contentEntry.Disable() // 禁用编辑但允许选择和复制
		contentEntry.Wrapping = fyne.TextWrapWord

		// 根据内容自动计算高度
		w.adjustEntryHeight(contentEntry, content)

		contentWidget = contentEntry
	}

	// 创建消息容器
	messageContainer := container.NewVBox(
		headerContainer,
		contentWidget,
		widget.NewSeparator(),
	)

	return messageContainer
}

// copyToClipboard 复制内容到剪贴板
func (w *WebView) copyToClipboard(content string) {
	// 获取应用的剪贴板
	clipboard := fyne.CurrentApp().Driver().AllWindows()[0].Clipboard()
	clipboard.SetContent(content)

	// 记录日志
	logger.Log.WithFields(logrus.Fields{
		"content_length": len(content),
	}).Info("内容已复制到剪贴板")

	// 如果有窗口引用，显示通知
	if w.window != nil {
		// 创建一个简单的通知对话框
		info := widget.NewLabel("✓ 内容已复制到剪贴板")
		info.Alignment = fyne.TextAlignCenter

		dialog := container.NewCenter(info)
		popup := widget.NewModalPopUp(dialog, w.window.Canvas())
		popup.Resize(fyne.NewSize(200, 60))
		popup.Show()

		// 1.5秒后自动关闭
		go func() {
			time.Sleep(1500 * time.Millisecond)
			popup.Hide()
		}()
	}
}

// calculateRichTextHeight 计算 RichText 组件的合适高度
func (w *WebView) calculateRichTextHeight(content string) float32 {
	// 计算文本行数
	lines := w.countLines(content)

	// 设置最小和最大行数
	minLines := 3  // 最少显示3行
	maxLines := 30 // 最大显示30行

	// 限制行数范围
	if lines < minLines {
		lines = minLines
	}
	if lines > maxLines {
		lines = maxLines
	}

	// RichText 每行大约25像素高度，加上一些边距
	lineHeight := 25.0
	padding := 20.0
	height := float32(float64(lines)*lineHeight + padding)

	// 设置最小高度
	minHeight := float32(80)
	if height < minHeight {
		height = minHeight
	}

	return height
}

// adjustEntryHeight 根据内容自动调整文本框高度
func (w *WebView) adjustEntryHeight(entry *widget.Entry, content string) {
	// 计算文本行数
	lines := w.countLines(content)

	// 设置最小和最大行数
	minLines := 2  // 最少显示2行
	maxLines := 20 // 最大显示20行，超过的会有滚动条

	// 限制行数范围
	if lines < minLines {
		lines = minLines
	}
	if lines > maxLines {
		lines = maxLines
	}

	// 每行大约20像素高度，加上一些边距
	lineHeight := 20.0
	padding := 20.0
	height := float32(float64(lines)*lineHeight + padding)

	// 设置最小高度
	minHeight := float32(60)
	if height < minHeight {
		height = minHeight
	}

	// 设置固定宽度，自适应高度
	width := float32(600)

	// 使用 MinSize 而不是 Resize 来确保高度自适应
	entry.Resize(fyne.NewSize(width, height))

	// 强制刷新组件
	entry.Refresh()
}

// countLines 计算文本的行数
func (w *WebView) countLines(content string) int {
	if content == "" {
		return 1
	}

	// 按换行符分割文本
	textLines := strings.Split(content, "\n")
	totalLines := 0

	// 每行可显示的字符数（考虑中英文混合）
	charsPerLine := 70 // 增加每行字符数以适应更宽的显示区域

	for _, line := range textLines {
		if line == "" {
			totalLines++ // 空行也占一行
			continue
		}

		// 计算这一行需要多少显示行
		runes := []rune(line)
		lineLength := len(runes)

		// 计算自动换行的行数
		wrappedLines := (lineLength + charsPerLine - 1) / charsPerLine
		if wrappedLines == 0 {
			wrappedLines = 1
		}

		totalLines += wrappedLines
	}

	return totalLines
}

// getLocalResponse 获取本地预设回复
func (w *WebView) getLocalResponse(message string) string {
	responses := map[string]string{
		"你好": "您好！很高兴为您服务。有什么我可以帮助您的吗？",
		"您好": "您好！很高兴为您服务。有什么我可以帮助您的吗？",
		"谢谢": "不客气！如果您还有其他问题，随时可以问我。",
		"感谢": "不客气！如果您还有其他问题，随时可以问我。",
		"再见": "再见！祝您工作顺利，有需要随时联系我。",
		"拜拜": "再见！祝您工作顺利，有需要随时联系我。",
		"帮助": "我可以帮助您解答各种问题，包括：\n- 系统使用指导\n- 数据上报相关问题\n- 技术支持\n- 其他工作相关咨询",
		"功能": "本系统主要功能包括：\n- 数据上报\n- 历史记录查看\n- 模板下载\n- 系统管理\n- AI对话助手",
		"配置": "要使用AI对话功能，请在配置管理中添加 'openrouter_api_key' 配置项，并设置您的 OpenRouter API 密钥。",
	}

	// 检查是否有预设回复
	for key, response := range responses {
		if message == key {
			return response
		}
	}

	// 默认回复
	return "我理解您的问题。基于您的输入「" + message + "」，我建议您可以：\n\n1. 查看相关的系统功能模块\n2. 参考帮助文档\n3. 如需更详细的帮助，请提供更多具体信息\n\n**提示：** 要启用完整的AI对话功能，请在配置管理中设置 'openrouter_api_key'。"
}

// CreateRenderer 实现fyne.Widget接口
func (w *WebView) CreateRenderer() fyne.WidgetRenderer {
	return &webViewRenderer{
		webView: w,
		content: w.content,
	}
}

// webViewRenderer WebView的渲染器
type webViewRenderer struct {
	webView *WebView
	content fyne.CanvasObject
}

func (r *webViewRenderer) Layout(size fyne.Size) {
	if r.content != nil {
		r.content.Resize(size)
	}
}

func (r *webViewRenderer) MinSize() fyne.Size {
	if r.content != nil {
		return r.content.MinSize()
	}
	return fyne.NewSize(400, 300)
}

func (r *webViewRenderer) Refresh() {
	r.content = r.webView.content
	if r.content != nil {
		r.content.Refresh()
	}
}

func (r *webViewRenderer) Objects() []fyne.CanvasObject {
	if r.content != nil {
		return []fyne.CanvasObject{r.content}
	}
	return []fyne.CanvasObject{}
}

func (r *webViewRenderer) Destroy() {
	// 清理资源
}
