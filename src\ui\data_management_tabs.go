package ui

import (
	"encoding/csv"
	"fmt"
	"os"
	"strconv"
	"strings"

	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"simple_inventory_management_system/internal/models"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"

	"github.com/sirupsen/logrus"
)

func (a *App) createAdminDivisionsTab() fyne.CanvasObject {
	// 搜索框
	searchEntry := newFixedWidthEntry(200) //widget.NewEntry()
	searchEntry.SetPlaceHolder("请输入单位名称搜索")

	// 分页控制变量
	currentPage := 1
	var totalCount int64 = 0
	var adminDivisions []models.AdminDivision

	// 加载数据函数
	loadData := func() {
		var err error
		adminDivisions, totalCount, err = database.SearchAdminDivisions(searchEntry.Text, PageSize, currentPage)
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"user":  a.user.Username,
			}).Error("加载行政区划数据失败")
			adminDivisions = []models.AdminDivision{}
		}
	}

	// 初始加载数据
	loadData()

	// 创建表格
	table := widget.NewTable(
		func() (int, int) {
			rowCount := len(adminDivisions) + 1 // +1 for header
			if rowCount < PageSize+1 {
				rowCount = PageSize + 1
			}
			return rowCount, 3 // 3 columns
		},
		func() fyne.CanvasObject {
			return container.NewStack(NewCopyableLabelWithoutText())
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			containerCell := obj.(*fyne.Container)
			label := containerCell.Objects[0].(*CopyableLabel)

			if id.Row == 0 {
				// Header row
				headers := []string{"序号", "单位代码", "单位名称"}
				if id.Col < len(headers) {
					label.SetText(headers[id.Col]) // 使用 SetText 而不是 label.SetText
				}
				return
			}

			// Data rows
			if id.Row-1 >= len(adminDivisions) {
				label.SetText("") // 使用 SetText
				return
			}

			adminDivision := adminDivisions[id.Row-1]
			switch id.Col {
			case 0:
				label.SetText(strconv.Itoa((currentPage-1)*PageSize + id.Row)) // 使用 SetText
			case 1:
				label.SetText(adminDivision.Code) // 使用 SetText
			case 2:
				label.SetText(adminDivision.UnitName) // 使用 SetText
			}
		},
	)

	// 设置列宽
	table.SetColumnWidth(0, 60)  // 序号
	table.SetColumnWidth(1, 150) // 单位代码
	table.SetColumnWidth(2, 300) // 单位名称
	// table.SetColumnWidth(3, 150) // 上级代码

	// 设置行高
	for i := 0; i < PageSize+1; i++ {
		table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
	}

	// 搜索按钮
	searchButton := widget.NewButton("搜索", func() {
		currentPage = 1 // 搜索时重置为第一页
		loadData()
		table.Refresh()
	})
	// 分页控制
	pageLabel := widget.NewLabel(fmt.Sprintf("第 %d 页，共 %d 条记录", currentPage, totalCount))

	// 刷新UI函数
	updatePageLabel := func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if pageCount == 0 {
			pageCount = 1
		}
		pageLabel.SetText(fmt.Sprintf("第 %d 页，共 %d 条记录，总共 %d 页", currentPage, totalCount, pageCount))
	}
	var importButton *widget.Button
	var clearButton *widget.Button
	if a.user != nil && a.user.Role == "管理员" {
		// 导入按钮
		importButton = widget.NewButton("导入数据", func() {
			a.showImportDialog("行政区划", func() {
				loadData()
				updatePageLabel()
				table.Refresh()
			})
		})

		// 清空按钮
		clearButton = widget.NewButton("清空数据", func() {
			dialog.ShowConfirm("确认清空", "确定要清空所有行政区划数据吗？", func(confirm bool) {
				if confirm {
					err := database.ClearAdminDivisions()
					if err != nil {
						dialog.ShowError(err, a.mainWin)
					} else {
						loadData()
						updatePageLabel()
						table.Refresh()
					}
				}
			}, a.mainWin)
		})
	} else {
		importButton = nil
		clearButton = nil
	}

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadData()
			updatePageLabel()
			table.Refresh()
		}
	})
	nextButton := widget.NewButton("下一页", func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if currentPage < int(pageCount) {
			currentPage++
			loadData()
			updatePageLabel()
			table.Refresh()
		}
	})

	// 创建顶部工具栏
	// topContainer := container.NewVBox(
	// 	container.NewHBox(
	// 		searchEntry,
	// 		searchButton,
	// 		importButton,
	// 		clearButton,
	// 	),
	// 	container.NewHBox(
	// 		prevButton,
	// 		pageLabel,
	// 		nextButton,
	// 	),
	// )

	searchComponents := []fyne.CanvasObject{
		searchEntry,
		searchButton,
	}

	if importButton != nil {
		searchComponents = append(searchComponents, importButton)
	}

	if clearButton != nil {
		searchComponents = append(searchComponents, clearButton)
	}

	searchContainer := container.NewVBox(
		container.NewHBox(searchComponents...),
		container.NewHBox(
			prevButton,
			pageLabel,
			nextButton,
		),
	)

	return container.NewBorder(
		searchContainer,
		nil, // 底部无内容
		nil, // 左侧无内容
		nil, // 右侧无内容
		table,
	)
}

func (a *App) createRegionsTab() fyne.CanvasObject {
	// 搜索框
	searchEntry := newFixedWidthEntry(200) //widget.NewEntry()
	searchEntry.SetPlaceHolder("请输入地区名称搜索")

	// 分页控制变量
	currentPage := 1
	var totalCount int64 = 0
	var regions []models.Region

	// 加载数据函数
	loadData := func() {
		var err error
		regions, totalCount, err = database.SearchRegions(searchEntry.Text, PageSize, currentPage)
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"user":  a.user.Username,
			}).Error("加载地区代码数据失败")
			regions = []models.Region{}
		}
	}

	// 初始加载数据
	loadData()

	// 创建表格
	table := widget.NewTable(
		func() (int, int) {
			rowCount := len(regions) + 1 // +1 for header
			if rowCount < PageSize+1 {
				rowCount = PageSize + 1
			}
			return rowCount, 3 // 3 columns
		},
		func() fyne.CanvasObject {
			return container.NewStack(NewCopyableLabelWithoutText())
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			containerCell := obj.(*fyne.Container)
			label := containerCell.Objects[0].(*CopyableLabel)

			if id.Row == 0 {
				// Header row
				headers := []string{"序号", "地区代码", "地区名称"}
				if id.Col < len(headers) {
					label.SetText(headers[id.Col])
				}
				return
			}

			// Data rows
			if id.Row-1 >= len(regions) {
				label.SetText("")
				return
			}

			region := regions[id.Row-1]
			switch id.Col {
			case 0:
				label.SetText(strconv.Itoa((currentPage-1)*PageSize + id.Row))
			case 1:
				label.SetText(region.Code)
			case 2:
				label.SetText(region.Name)
			}
		},
	)

	// 设置列宽
	table.SetColumnWidth(0, 60)  // 序号
	table.SetColumnWidth(1, 150) // 地区代码
	table.SetColumnWidth(2, 300) // 地区名称

	// 设置行高
	for i := 0; i < PageSize+1; i++ {
		table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
	}

	// 分页控制
	pageLabel := widget.NewLabel(fmt.Sprintf("第 %d 页，共 %d 页", currentPage, (totalCount+PageSize-1)/PageSize))
	updatePageLabel := func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if pageCount == 0 {
			pageCount = 1
		}
		pageLabel.SetText(fmt.Sprintf("第 %d 页，共 %d 页", currentPage, pageCount))
	}
	// 搜索按钮
	searchButton := widget.NewButton("搜索", func() {
		currentPage = 1 // 搜索时重置为第一页
		loadData()
		updatePageLabel()
		table.Refresh()
	})

	var importButton *widget.Button
	var clearButton *widget.Button
	if a.user != nil && a.user.Role == "管理员" {
		// 导入按钮
		importButton = widget.NewButton("导入数据", func() {
			a.showImportDialog("地区代码", func() {
				loadData()
				updatePageLabel()
				table.Refresh()
			})
		})

		// 清空按钮
		clearButton = widget.NewButton("清空数据", func() {
			dialog.ShowConfirm("确认清空", "确定要清空所有地区代码数据吗？", func(confirm bool) {
				if confirm {
					err := database.ClearRegions()
					if err != nil {
						dialog.ShowError(err, a.mainWin)
					} else {
						loadData()
						updatePageLabel()
						table.Refresh()
					}
				}
			}, a.mainWin)
		})
	} else {
		importButton = nil
		clearButton = nil
	}

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadData()
			updatePageLabel()
			table.Refresh()
		}
	})
	nextButton := widget.NewButton("下一页", func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if currentPage < int(pageCount) {
			currentPage++
			loadData()
			updatePageLabel()
			table.Refresh()
		}
	})

	// 创建顶部工具栏
	// topContainer := container.NewVBox(
	// 	container.NewHBox(
	// 		searchEntry,
	// 		searchButton,
	// 		importButton,
	// 		clearButton,
	// 	),
	// 	container.NewHBox(
	// 		prevButton,
	// 		pageLabel,
	// 		nextButton,
	// 	),
	// )

	// 创建搜索和分页控制容器
	searchComponents := []fyne.CanvasObject{
		searchEntry,
		searchButton,
	}

	if importButton != nil {
		searchComponents = append(searchComponents, importButton)
	}

	if clearButton != nil {
		searchComponents = append(searchComponents, clearButton)
	}

	searchContainer := container.NewVBox(
		container.NewHBox(searchComponents...),
		container.NewHBox(
			prevButton,
			pageLabel,
			nextButton,
		),
	)

	return container.NewBorder(
		searchContainer,
		nil, // 底部无内容
		nil, // 左侧无内容
		nil, // 右侧无内容
		table,
	)
}

func (a *App) createCategoriesTab() fyne.CanvasObject {
	// 搜索框
	categoryCodeEntry := newFixedWidthEntry(200) //widget.NewEntry()
	categoryCodeEntry.SetPlaceHolder("品类编码")
	categoryNameEntry := newFixedWidthEntry(200) //widget.NewEntry()
	categoryNameEntry.SetPlaceHolder("品类名称")
	// categoryTypeSelect := widget.NewSelect([]string{"", "一级", "二级", "三级"}, nil)
	// categoryTypeSelect.PlaceHolder = "品类级别"
	var totalCount int64 = 0
	// 分页控制变量
	currentPage := 1
	var categories []models.Category

	// 加载数据函数
	loadData := func() {
		var err error
		categories, totalCount, err = database.SearchCategories(categoryCodeEntry.Text, "", categoryNameEntry.Text, PageSize, currentPage)
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"user":  a.user.Username,
			}).Error("加载品类数据失败")
			categories = []models.Category{}
		}
	}

	// 初始加载数据
	loadData()

	// 创建表格
	table := widget.NewTable(
		func() (int, int) {
			rowCount := len(categories) + 1 // +1 for header
			if rowCount < PageSize+1 {
				rowCount = PageSize + 1
			}
			return rowCount, 5 // 5 columns
		},
		func() fyne.CanvasObject {
			return container.NewStack(NewCopyableLabelWithoutText())
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			containerCell := obj.(*fyne.Container)
			label := containerCell.Objects[0].(*CopyableLabel)
			if id.Row == 0 {
				// Header row
				headers := []string{"序号", "品类编码", "上级编码", "品类名称", "品类级别"}
				if id.Col < len(headers) {
					label.SetText(headers[id.Col])
				}
				return
			}

			// Data rows
			if id.Row-1 >= len(categories) {
				label.SetText("")
				return
			}

			category := categories[id.Row-1]
			switch id.Col {
			case 0:
				label.SetText(strconv.Itoa((currentPage-1)*PageSize + id.Row))
			case 1:
				label.SetText(category.CategoryCode)
			case 2:
				label.SetText(category.ParentCode)
			case 3:
				label.SetText(category.CategoryName)
			case 4:
				label.SetText(category.CategoryType)
			}
		},
	)

	// 设置列宽
	table.SetColumnWidth(0, 60)  // 序号
	table.SetColumnWidth(1, 150) // 品类编码
	table.SetColumnWidth(2, 150) // 品类名称
	table.SetColumnWidth(3, 200) // 品类级别
	table.SetColumnWidth(4, 100) // 上级编码

	// 设置行高
	for i := 0; i < PageSize+1; i++ {
		table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
	}
	// 分页控制
	pageLabel := widget.NewLabel(fmt.Sprintf("第 %d 页，共 %d 页", currentPage, (totalCount+PageSize-1)/PageSize))

	updatePageLabel := func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if pageCount == 0 {
			pageCount = 1
		}
		pageLabel.SetText(fmt.Sprintf("第 %d 页，共 %d 页", currentPage, pageCount))
	}
	// 搜索按钮
	searchButton := widget.NewButton("搜索", func() {
		currentPage = 1 // 搜索时重置为第一页
		loadData()
		updatePageLabel()
		table.Refresh()
	})
	var importButton *widget.Button
	var clearButton *widget.Button
	if a.user != nil && a.user.Role == "管理员" {
		// 导入按钮
		importButton = widget.NewButton("导入数据", func() {
			a.showImportDialog("品类管理", func() {
				// 刷新数据和UI
				currentPage = 1
				loadData()
				updatePageLabel()
				table.Refresh()
			})
		})

		// 清空按钮
		clearButton = widget.NewButton("清空数据", func() {
			dialog.ShowConfirm("确认清空", "确定要清空所有品类数据吗？", func(confirm bool) {
				if confirm {
					err := database.ClearCategories()
					if err != nil {
						dialog.ShowError(err, a.mainWin)
					} else {
						// 刷新数据和UI
						currentPage = 1
						loadData()
						updatePageLabel()
						table.Refresh()
					}
				}
			}, a.mainWin)
		})
	} else {
		importButton = nil
		clearButton = nil
	}

	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadData()
			updatePageLabel()
			table.Refresh()
		}
	})
	nextButton := widget.NewButton("下一页", func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if currentPage < int(pageCount) {
			currentPage++
			loadData()
			updatePageLabel()
			table.Refresh()
		}
	})

	// 创建搜索和分页控制容器
	searchComponents := []fyne.CanvasObject{
		categoryCodeEntry,
		categoryNameEntry,
		// categoryTypeSelect,
		searchButton,
	}

	if importButton != nil {
		searchComponents = append(searchComponents, importButton)
	}

	if clearButton != nil {
		searchComponents = append(searchComponents, clearButton)
	}

	searchContainer := container.NewVBox(
		container.NewHBox(searchComponents...),
		container.NewHBox(
			prevButton,
			pageLabel,
			nextButton,
		),
	)

	return container.NewBorder(
		searchContainer,
		nil, // 底部无内容
		nil, // 左侧无内容
		nil, // 右侧无内容
		table,
	)
}

func (a *App) showImportDialog(tableType string, onComplete func()) {
	filePathEntry := newFixedWidthEntry(200)
	filePathEntry.SetPlaceHolder("请选择要导入的CSV文件")

	fileSelectButton := widget.NewButton("选择文件", func() {
		dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil || reader == nil {
				return
			}
			filePathEntry.SetText(reader.URI().Path())
		}, a.mainWin)
	})

	resultLabel := widget.NewLabel("")

	importButton := widget.NewButton("开始导入", func() {
		filePath := filePathEntry.Text
		if filePath == "" {
			resultLabel.SetText("请选择文件")
			return
		}

		var count int
		var err error

		switch tableType {
		case "行政区划":
			count, err = a.importAdminDivisions(filePath)
		case "地区代码":
			count, err = a.importRegions(filePath)
		case "品类管理":
			count, err = a.importCategories(filePath)
		default:
			err = fmt.Errorf("未知的表类型: %s", tableType)
		}

		if err != nil {
			resultLabel.SetText(fmt.Sprintf("导入失败: %v", err))
		} else {
			resultLabel.SetText(fmt.Sprintf("导入成功，共导入 %d 条记录", count))
			onComplete()
		}
	})

	content := container.NewVBox(
		widget.NewForm(
			widget.NewFormItem("CSV文件", container.NewBorder(nil, nil, nil, fileSelectButton, filePathEntry)),
		),
		importButton,
		resultLabel,
	)

	dialog.ShowCustom(fmt.Sprintf("导入%s数据", tableType), "关闭", content, a.mainWin)
}

func checkCharset(f *os.File) *encoding.Decoder {
	// 读取文件前几个字节来检测编码
	buf := make([]byte, 1024)
	n, _ := f.Read(buf)
	f.Seek(0, 0) // 重置文件指针

	// 检测是否为UTF-8 BOM
	if n >= 3 && buf[0] == 0xEF && buf[1] == 0xBB && buf[2] == 0xBF {
		return nil // UTF-8 with BOM
	}

	// 简单检测：如果包含中文字符且不是有效UTF-8，则认为是GBK
	for i := 0; i < n; i++ {
		if buf[i] > 127 { // 非ASCII字符
			// 尝试作为UTF-8解码
			if !isValidUTF8(buf[:n]) {
				return simplifiedchinese.GBK.NewDecoder()
			}
			break
		}
	}

	return nil // UTF-8
}

func isValidUTF8(data []byte) bool {
	// 简单的UTF-8验证
	for i := 0; i < len(data); {
		if data[i] < 128 {
			i++
			continue
		}
		// 多字节字符检查
		if data[i]&0xE0 == 0xC0 { // 2字节
			if i+1 >= len(data) || data[i+1]&0xC0 != 0x80 {
				return false
			}
			i += 2
		} else if data[i]&0xF0 == 0xE0 { // 3字节
			if i+2 >= len(data) || data[i+1]&0xC0 != 0x80 || data[i+2]&0xC0 != 0x80 {
				return false
			}
			i += 3
		} else if data[i]&0xF8 == 0xF0 { // 4字节
			if i+3 >= len(data) || data[i+1]&0xC0 != 0x80 || data[i+2]&0xC0 != 0x80 || data[i+3]&0xC0 != 0x80 {
				return false
			}
			i += 4
		} else {
			return false
		}
	}
	return true
}

func (a *App) importAdminDivisions(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	// 检测字符编码
	decoder := checkCharset(file)
	var reader *csv.Reader
	if decoder != nil {
		reader = csv.NewReader(transform.NewReader(file, decoder))
	} else {
		reader = csv.NewReader(file)
	}

	records, err := reader.ReadAll()
	if err != nil {
		return 0, err
	}

	db := database.GetDB()
	if db == nil {
		return 0, fmt.Errorf("数据库连接失败")
	}

	count := 0
	var admindivisionList []models.AdminDivision
	for i, record := range records {
		if i == 0 {
			continue // 跳过标题行
		}
		// if len(record) < 3 {
		// 	continue // 跳过不完整的记录
		// }

		unitCode := enhancedTrim(record[0])
		unitName := enhancedTrim(record[1])

		if unitCode == "" || unitName == "" {
			continue
		}

		if strings.Contains(unitName, "?") || strings.Contains(unitName, "？") {
			unitName = strings.ReplaceAll(unitName, "?", "")
			unitName = strings.ReplaceAll(unitName, "？", "")
		}
		admindivisionList = append(admindivisionList, models.AdminDivision{
			Code:      unitCode,
			UnitName:  unitName,
			IsDeleted: 0,
		})
		count++
	}
	err = database.BatchAddAdminDivision(admindivisionList)
	// _, err := db.Exec(`INSERT OR REPLACE INTO admin_divisions (code, unit_name, is_deleted) VALUES (?, ?, 0)`,
	// 	unitCode, unitName)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":             err,
			"admindivisionList": len(admindivisionList),
		}).Error("插入行政区划数据失败")
	}

	return count, nil
}

func (a *App) importRegions(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	// 检测字符编码
	decoder := checkCharset(file)
	var reader *csv.Reader
	if decoder != nil {
		reader = csv.NewReader(transform.NewReader(file, decoder))
	} else {
		reader = csv.NewReader(file)
	}

	records, err := reader.ReadAll()
	if err != nil {
		return 0, err
	}

	db := database.GetDB()
	if db == nil {
		return 0, fmt.Errorf("数据库连接失败")
	}

	count := 0
	var regions []models.Region
	for i, record := range records {
		if i == 0 {
			continue // 跳过标题行
		}
		if len(record) < 2 {
			continue // 跳过不完整的记录
		}

		regionCode := enhancedTrim(record[0])
		regionName := enhancedTrim(record[1])

		if regionCode == "" || regionName == "" {
			continue
		}

		regions = append(regions, models.Region{
			Code: regionCode,
			Name: regionName,
		})
		count++
	}
	_, err = database.BatchAddRegions(regions)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":   err,
			"regions": len(regions),
		}).Error("插入地区数据失败")
	}
	return count, nil
}

func (a *App) importCategories(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	// 检测字符编码
	decoder := checkCharset(file)
	var reader *csv.Reader
	if decoder != nil {
		reader = csv.NewReader(transform.NewReader(file, decoder))
	} else {
		reader = csv.NewReader(file)
	}

	records, err := reader.ReadAll()
	if err != nil {
		return 0, err
	}

	db := database.GetDB()
	if db == nil {
		return 0, fmt.Errorf("数据库连接失败")
	}

	count := 0
	var categories []models.Category
	for i, record := range records {
		if i == 0 {
			continue // 跳过标题行
		}
		if len(record) < 4 {
			continue // 跳过不完整的记录
		}

		// categoryCode := enhancedTrim(record[0])
		// categoryName := enhancedTrim(record[1])
		// categoryType := enhancedTrim(record[2])
		// parentCode := enhancedTrim(record[3])

		categoryCode := enhancedTrim(record[0])
		categoryName := enhancedTrim(record[1])
		parentCode := enhancedTrim(record[2])
		industryType := enhancedTrim(record[3])
		priceUnit := enhancedTrim(record[4])
		quantityUnit := enhancedTrim(record[5])
		categoryType := ""
		if parentCode == "" {
			categoryType = "1"
		} else if len(parentCode) < 6 {
			categoryType = "2"
		} else if len(parentCode) < 9 {
			categoryType = "3"
		}

		if categoryCode == "" || categoryName == "" {
			continue
		}

		categories = append(categories, models.Category{
			CategoryCode: categoryCode,
			CategoryName: categoryName,
			CategoryType: categoryType,
			ParentCode:   parentCode,
			IndustryType: industryType,
			PriceUnit:    priceUnit,
			QuantityUnit: quantityUnit,
		})

		count++
	}
	// err = database.AddCategory(categoryCode, categoryName, parentCode, industryType, priceUnit, quantityUnit, categoryType)
	// _, err := db.Exec(`INSERT OR REPLACE INTO categories (category_code, category_name, parent_code, industry_type, price_unit, quantity_unit) VALUES (?, ?, ?, ?, ?, ?) ON CONFLICT(category_code) DO UPDATE SET category_name = ?, parent_code = ?, industry_type = ?, price_unit = ?, quantity_unit = ?`,
	// 	categoryCode, categoryName, parentCode, industryType, priceUnit, quantityUnit,
	// 	categoryName, parentCode, industryType, priceUnit, quantityUnit)
	err = database.BatchAddCategory(categories)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":      err,
			"categories": len(categories),
		}).Error("插入品类数据失败")
	}

	return count, nil
}
