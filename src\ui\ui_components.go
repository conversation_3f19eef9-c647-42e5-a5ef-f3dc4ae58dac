package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/widget"
)

// HoverableLabel 是一个自定义组件，当鼠标悬浮时会显示一个包含完整文本的弹出框
type HoverableLabel struct {
	widget.Label
	fullText string
	canvas   fyne.Canvas
	popup    fyne.CanvasObject
	maxWidth float32
}

// NewHoverableLabel 创建一个新的 HoverableLabel 实例
func NewHoverableLabel(canvas fyne.Canvas) *HoverableLabel {
	h := &HoverableLabel{
		canvas: canvas,
	}
	// 关键步骤：扩展基础组件，使其能够接收事件
	h.ExtendBaseWidget(h)
	return h
}

// SetText 设置标签的显示文本和悬浮时要显示的完整文本
func (h *HoverableLabel) SetText(text string) {
	h.fullText = text
	// 截断显示的文本以适应列宽
	displayText := h.truncateText(text)
	h.Label.SetText(displayText)
}

// truncateText 根据可用宽度截断文本
func (h *HoverableLabel) truncateText(text string) string {
	if text == "" {
		return text
	}

	// 如果文本长度超过一定字符数，进行截断
	maxChars := 30 // 根据列宽调整这个值
	if len([]rune(text)) > maxChars {
		runes := []rune(text)
		return string(runes[:maxChars-3]) + "..."
	}
	return text
}

// MouseIn 当鼠标进入组件区域时触发
func (h *HoverableLabel) MouseIn(e *desktop.MouseEvent) {
	// 只有当文本被截断时才显示弹出框
	if h.popup != nil || h.fullText == "" || !h.isTextTruncated() {
		return
	}

	// 创建一个富文本标签，它可以自动换行
	popupContent := widget.NewRichTextWithText(h.fullText)
	popupContent.Wrapping = fyne.TextWrapWord

	// 使用 Card 组件来给弹出框添加背景和阴影，使其更明显
	h.popup = widget.NewCard("", "", popupContent)

	// 设置弹出框的最大宽度
	h.popup.Resize(fyne.NewSize(400, 0))

	// 将弹出框添加到画布的浮层上
	h.canvas.Overlays().Add(h.popup)

	// 定位弹出框在鼠标指针的右下方
	// e.AbsolutePosition 是相对于整个画布的绝对位置
	pos := e.AbsolutePosition
	pos.X += 10 // x方向偏移一点，避免遮挡鼠标
	pos.Y += 10 // y方向偏移一点
	h.popup.Move(pos)
}

// MouseOut 当鼠标离开组件区域时触发
func (h *HoverableLabel) MouseOut() {
	// 如果存在弹出框，则从浮层中移除并销毁
	if h.popup != nil {
		h.canvas.Overlays().Remove(h.popup)
		h.popup = nil
	}
}

// isTextTruncated 检查文本是否被截断
func (h *HoverableLabel) isTextTruncated() bool {
	return len([]rune(h.fullText)) > 30
}

// MouseMoved 实现 desktop.Hoverable 接口
func (h *HoverableLabel) MouseMoved(*desktop.MouseEvent) {
	// 空实现，但需要存在以满足接口要求
}

// Tapped 是为了满足 fyne.Tappable 接口，如果你的Label需要点击，可以在这里实现
func (h *HoverableLabel) Tapped(*fyne.PointEvent) {
	// 目前不需要点击功能，留空
}

// TappedSecondary 是为了满足 fyne.SecondaryTappable 接口（右键点击）
func (h *HoverableLabel) TappedSecondary(*fyne.PointEvent) {
	// 留空
}
