package ui

import (
	"image/color"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/canvas"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/widget"
)

// HoverableLabel 是一个自定义组件，当鼠标悬浮时会显示一个包含完整文本的弹出框
type HoverableLabel struct {
	widget.Label
	fullText string
	canvas   fyne.Canvas
	popup    fyne.CanvasObject
	maxWidth float32
}

// NewHoverableLabel 创建一个新的 HoverableLabel 实例
func NewHoverableLabel(canvas fyne.Canvas) *HoverableLabel {
	h := &HoverableLabel{
		canvas: canvas,
	}
	// 关键步骤：扩展基础组件，使其能够接收事件
	h.ExtendBaseWidget(h)

	// 确保组件可以接收鼠标事件
	h.Label.Wrapping = fyne.TextWrapOff

	return h
}

// SetText 设置标签的显示文本和悬浮时要显示的完整文本
func (h *HoverableLabel) SetText(text string) {
	// 清除之前的弹出框
	if h.popup != nil {
		h.canvas.Overlays().Remove(h.popup)
		h.popup = nil
	}

	h.fullText = text
	// 截断显示的文本以适应列宽
	displayText := h.truncateText(text)
	h.Label.SetText(displayText)
}

// truncateText 根据可用宽度截断文本
func (h *HoverableLabel) truncateText(text string) string {
	if text == "" {
		return text
	}

	// 根据文本长度动态调整截断长度
	maxChars := 20 // 默认截断长度，适合大多数列

	// 对于很长的文本（如错误信息），使用更大的截断长度
	if len([]rune(text)) > 80 {
		maxChars = 80
	} else if len([]rune(text)) > 50 {
		maxChars = 50
	}

	if len([]rune(text)) > maxChars {
		runes := []rune(text)
		return string(runes[:maxChars-3]) + "..."
	}
	return text
}

// MouseIn 当鼠标进入组件区域时触发
func (h *HoverableLabel) MouseIn(e *desktop.MouseEvent) {
	// 确保画布存在
	if h.canvas == nil {
		return
	}

	// 如果已经有弹出框，先清除
	if h.popup != nil {
		h.canvas.Overlays().Remove(h.popup)
		h.popup = nil
	}

	// 只有当文本被截断时才显示弹出框
	if h.fullText == "" || !h.isTextTruncated() {
		return
	}

	// 创建一个普通标签
	popupContent := widget.NewLabel(h.fullText)
	popupContent.Wrapping = fyne.TextWrapWord

	// 创建一个不透明的白色背景矩形
	background := canvas.NewRectangle(color.RGBA{R: 255, G: 255, B: 255, A: 240}) // 几乎不透明的白色

	// 创建容器，先放背景，再放内容
	h.popup = container.NewStack(
		background,
		container.NewPadded(popupContent),
	)

	// 设置弹出框的大小
	h.popup.Resize(fyne.NewSize(400, 100))

	// 将弹出框添加到画布的浮层上
	h.canvas.Overlays().Add(h.popup)

	// 定位弹出框在鼠标指针的右下方
	// e.AbsolutePosition 是相对于整个画布的绝对位置
	pos := e.AbsolutePosition
	pos.X += 10 // x方向偏移一点，避免遮挡鼠标
	pos.Y += 10 // y方向偏移一点
	h.popup.Move(pos)
}

// MouseOut 当鼠标离开组件区域时触发
func (h *HoverableLabel) MouseOut() {
	// 如果存在弹出框，则从浮层中移除并销毁
	if h.popup != nil {
		h.canvas.Overlays().Remove(h.popup)
		h.popup = nil
	}
}

// isTextTruncated 检查文本是否被截断
func (h *HoverableLabel) isTextTruncated() bool {
	if h.fullText == "" {
		return false
	}

	// 使用与 truncateText 相同的逻辑来判断是否被截断
	maxChars := 20
	textLen := len([]rune(h.fullText))

	if textLen > 100 {
		maxChars = 50
	} else if textLen > 50 {
		maxChars = 30
	}

	return textLen > maxChars
}

// MouseMoved 实现 desktop.Hoverable 接口
func (h *HoverableLabel) MouseMoved(*desktop.MouseEvent) {
	// 空实现，但需要存在以满足接口要求
}

// Tapped 是为了满足 fyne.Tappable 接口，如果你的Label需要点击，可以在这里实现
func (h *HoverableLabel) Tapped(*fyne.PointEvent) {
	// 目前不需要点击功能，留空
}

// TappedSecondary 是为了满足 fyne.SecondaryTappable 接口（右键点击）
func (h *HoverableLabel) TappedSecondary(*fyne.PointEvent) {
	// 留空
}
