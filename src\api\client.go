package api

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
)

// APIClient is a client for interacting with the API.
// It holds the necessary configuration for making requests.
type APIClient struct {
	BaseURL    string
	SecretID   string
	SecretKey  string
	HTTPClient *http.Client
}

// NewClient creates a new API client.
func NewClient(baseURL, secretID, secretKey string) *APIClient {
	return &APIClient{
		BaseURL:   baseURL,
		SecretID:  secretID,
		SecretKey: secretKey,
		HTTPClient: &http.Client{
			Timeout: time.Minute,
		},
	}
}

// sendRequest is a generic method to send requests to the API.
// It handles signing the request and decoding the response.
func (c *APIClient) sendRequest(endpoint string, requestBody interface{}, responseBody interface{}) error {
	timestamp := strconv.FormatInt(time.Now().UnixMilli(), 10)
	sign := GenerateSign(c.SecretKey, timestamp)

	url := fmt.Sprintf("%s%s", c.BaseURL, endpoint)

	body, err := json.Marshal(requestBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("secretId", c.SecretID)
	req.Header.Set("sign", sign)
	req.Header.Set("timestamp", timestamp)

	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if err := json.NewDecoder(resp.Body).Decode(responseBody); err != nil {
		return fmt.Errorf("failed to decode response body: %w", err)
	}

	return nil
}

// SubmitPriceReport sends a price report to the API.
func (c *APIClient) SubmitPriceReport(req PriceReportRequest) (*CommonResponse, error) {
	var resp CommonResponse
	err := c.sendRequest("/mmsPriceRptTest", req, &resp)
	return &resp, err
}

// SubmitInventoryReport sends an inventory report to the API.
func (c *APIClient) SubmitInventoryReport(req InventoryReportRequest) (*CommonResponse, error) {
	var resp CommonResponse
	err := c.sendRequest("/ecdbStoreDayRptTest", req, &resp)
	return &resp, err
}

// SubmitFoodProcessingReport sends a food processing report to the API.
func (c *APIClient) SubmitFoodProcessingReport(req FoodProcessingReportRequest) (*CommonResponse, error) {
	var resp CommonResponse
	err := c.sendRequest("/ecdbProductMonthRptTest", req, &resp)
	return &resp, err
}

// SubmitLogisticsReport sends a logistics report to the API.
func (c *APIClient) SubmitLogisticsReport(req LogisticsReportRequest) (*CommonResponse, error) {
	var resp CommonResponse
	err := c.sendRequest("/transportRptTest", req, &resp)
	return &resp, err
}

// SubmitDeliveryReport sends a delivery report to the API.
func (c *APIClient) SubmitDeliveryReport(req DeliveryReportRequest) (*CommonResponse, error) {
	var resp CommonResponse
	err := c.sendRequest("/deliverRptTest", req, &resp)
	return &resp, err
}

// SubmitNodeInventoryReport sends a node-level inventory report to the API.
func (c *APIClient) SubmitNodeInventoryReport(req NodeInventoryReportRequest) (*CommonResponse, error) {
	var resp CommonResponse
	// Note: The URL for this endpoint is different according to the document.
	// You might need a separate client or logic to handle the different base URL.
	err := c.sendRequest("/proxy/sgp/subject/uploadProductDtl", req, &resp)
	return &resp, err
}