1.  **认证签名机制:** 所有接口的调用都需要通过`secretId`, `secretKey`, 和 `timestamp` 生成签名`sign`。提供了一个名为 `generateSign` 的方法用于生成签名。

2.  **多个API接口:** 文档列出了多个用于不同维度的数据上报接口，包括：
    *   **市场监测日度商超价格数据信息**
    *   **市场监测日度批发价格数据信息**
    *   **市场监测日度农贸价格数据信息**
    *   **应急保供日度商超进销存数据信息**
    *   **应急保供日度批发进销存数据信息**
    *   **食品加工企业月度数据信息**
    *   **物流运输信息监测数据**
    *   **终端配送信息监测数据**

3.  **接口通用结构:**
    *   **请求方法:** 均为 `POST`。
    *   **接口地址:** 测试环境地址为 `http://58.48.136.183:19090/hbyjbg-api/model/v2/`，部分接口有不同的路径后缀（如`mmsPriceRptTest`），但也有部分网点维度的接口地址指向了`http://58.48.136.183:1990/proxy/sgp/subject/`下的不同路径。
    *   **请求头 (Header) 参数:** `secretId`, `sign`, `timestamp` 是所有接口共有的认证参数。
    *   **请求体 (Body) 参数:** `.json` 格式，包含业务数据。`loginName`、`commodityId`、`rptdate` 是 اکثر接口共有的核心业务参数。`commodityId` 需要参考附录的品类字典表。
    *   **响应参数:** 通用响应结构，包含 `code` (应答标志) 和 `message` (应答信息)，其中`code` 200表示成功，其他值表示不同的错误或异常情况。

4.  **附录信息:** 提供了重要的参考数据字典：
    *   **市场监测品类字典表:** 包含商超和批发市场的品类编码、名称、父类编码、行业类型、价格单位和数量单位。强调了父类上传的要求。
    *   **应急保供零售品类字典表:** 针对零售市场的品类编码、名称、父类编码和数量单位。
    *   **应急保供批发品类字典表:** 针对批发市场的品类编码、名称、父类编码和数量单位。
    *   **食品加工品类字典表:** 包含食品加工领域的品类编码、名称、父类编码和数量单位，并说明了报送规则。
    *   **物流数据品类字典表:** 物流相关物品的编码、名称和单位。
    *   **行政区划字典表:** 详细列出了中国各省、市、县的行政区划代码。
    *   **《世界各国和地区名称代码》（GB/T 2659）:** 用于国际产地编码。

**基于以上解析，以下是使用Golang开发功能手册的总结：**

---

## Golang API 调用功能手册 (基于接口文档)

### 1. 项目概述

本手册旨在指导开发者如何使用Golang调用提供的各项数据上报API接口。这些接口用于收集市场监测、应急保供以及物流运输等相关数据。

### 2. 认证签名

所有接口调用均需要进行认证签名。调用方需根据以下流程生成签名 `sign`：

*   **获取密钥:** 公司会为每个对接服务提供一对唯一的 `secretId` 和 `secretKey`。请妥善保管，避免泄露。
*   **获取时间戳:** 调用`System.currentTimeMillis()`方法获取当前时间的毫秒级时间戳。
*   **生成签名:**
    *   将`secretKey`和`timestamp`（需要是String类型）按照特定规则进行组合（具体组合方式请参考文档中的“认证签名工具”中的`generateSign`方法）。
    *   调用`generateSign`方法生成最终的`sign`。

### 3. 通用请求结构

所有接口均采用 `POST` 请求，请求体格式为 `application/json`。

#### 3.1 请求头 (Headers)

在HTTP请求头中必须包含以下参数：

|   参数名    |  类型  | 是否必填 | 说明                                   |
| :---------: | :----: | :------: | :------------------------------------- |
| `secretId`  | string |    是    | 在认证签名部分生成                     |
|   `sign`    | string |    是    | 调用认证签名工具生成                   |
| `timestamp` | string |    是    | 获取方式：`System.currentTimeMillis()` |

#### 3.2 请求体 (Request Body / Body)

请求体为JSON对象，结构根据具体的API接口有所不同。核心通用字段包括：

*   `loginName` (String, 必填): 企业统一平台编码/登录编码。
*   `commodityId` (String, 必填): 品类编码，具体编码可在附录的品类字典表中查找。
*   `rptdate` (String, 必填): 报表日期，格式为 `"yyyy-MM-dd"` 或 `"yyyy-MM-dd hh24:mi:ss"`。
*   `createTime` (String, 必填): 报送日期，格式为 `"yyyy-MM-dd hh24:mi:ss"`。
*   `updateTime` (String, 可选): 修改时间，格式为 `"yyyy-MM-dd hh24:mi:ss"`，若无则传 `null` 或空字符串。
*   `linkman` (String, 必填): 填报人姓名。
*   `telephone` (String, 必填): 联系人电话。
*   `memo` (String, 可选): 简要分析。
*   `leader` (String, 必填): 单位负责人。
*   `statistician` (String, 必填): 统计负责人。

**网点维度接口特有参数:**
*   `nodeCode` (String, 可选): 网点编码，与 `foreignId` 不能同时为空。
*   `foreignId` (String, 可选): 网点外部编码，与 `nodeCode` 不能同时为空。
*   `originPlace` (Number, 必填): 出发/产地省份/地区编码，需查阅行政区划字典表。
*   `manufacturerName` (String, 可选): 生产商名称。
*   `manufacturerAddress` (String, 可选): 生产商地址。
*   `vehicle` (String, 必填): 车牌号。
*   `driver` (String, 必填): 司机姓名。
*   `driverMobile` (String, 必填): 司机手机号。
*   `departProvince`, `departCity`, `departCounty` (Number, 必填): 出发地行政区划编码。
*   `departPlace` (String, 必填): 出发地详细地址。
*   `departTime` (String, 必填): 出发时间。
*   `arrivalProvince`, `arrivalCity`, `arrivalCounty` (Number, 必填): 到达地行政区划编码。
*   `arrivalPlace` (String, 必填): 到达地详细地址。
*   `arrivalTime` (String, 必填): 预计到达时间。
*   `isArrival` (Number, 必填): 是否到达 (1-是，0-不是)。
*   `isFreeze` (Number, 必填): 是否冷链 (1-是，0-不是)。
*   `amount` (Number, 必填): 销售量/交易量。
*   `instock` (Number, 必填): 库存量/进货量。
*   `stock` (Number, 必填): 库存量/销售量。（注意：此字段在不同接口中有时代表库存量，有时代表销售量，需对照接口定义）
*   `totalStore` (Number, 必填): 库存量。
*   `totalProduction` (Number, 必填): 生产量。
*   `totalSales` (Number, 必填): 销售量。
*   `dayProduction` (Number, 必填): 日产能。
*   `takeOutAmount` (Number, 必填): 可调出量。
*   `waybill` (String, 必填): 唯一配送运单编码。
*   `courier` (String, 必填): 配送员姓名。
*   `courierMobile` (String, 必填): 配送员手机号码。
*   `departDepot` (String, 必填): 出发仓。
*   `arrivalAreaName` (String, 必填): 到达点位。

#### 3.3reqParam 样例

请求体通常包含一个 `items` 数组，每个数组元素代表一条记录。

```json
// 示例：市场监测日度商超价格数据
{
  "items": [
    {
      "loginName": "420107000001",
      "price": "30",
      "commodityId": "1101",
      "rptdate": "1999-07-24",
      "updateTime": "1999-07-24 11:11:11",
      "createTime": "1999-07-24 11:11:11",
      "linkman": "张三",
      "telephone": "13800138000",
      "memo": "受季节天气降温因素影响，市场对蔬菜需求量上升，市场部分蔬菜价格上涨。",
      "leader": "赵四",
      "statistician": "王五"
    }
    // ... 更多记录
  ]
}
```

### 4. 响应结构

| 参数标识  |  类型  | 说明                                                                            |
| :-------: | :----: | :------------------------------------------------------------------------------ |
|  `code`   | String | 200: 成功, 400: 服务异常, 401: 签名认证失败, 403: 权限不足, 500: 服务器内部异常 |
| `message` | String | 操作成功/请求失败原因                                                           |

### 5. Golang 实现建议

#### 5.1 认证签名实现

*   创建一个 Go 模块来封装签名生成逻辑，提供一个 `GenerateSign(secretId, secretKey, timestamp string) string` 方法。
*   注意 `timestamp` 需要是 String 类型。

#### 5.2 请求构造

*   针对每个 API 接口，定义对应的 Go 结构体来表示请求体参数。
*   使用 `json.Marshal` 将结构体转换为 JSON 字符串。
*   对于 Header 参数，可以使用 `net/http` 包的 `Request.Header.Set()` 方法设置。

#### 5.3 HTTP 请求发送

*   使用 `net/http` 包发送 `POST` 请求。
*   设置 `Content-Type` 为 `application/json`。
*   处理 HTTP 响应，解析 `code` 和 `message` 来判断操作结果。

#### 5.4 错误处理

*   对 HTTP 请求错误、签名认证失败（`code: 401`）、服务异常（`code: 400`, `code: 500`）等情况进行统一处理和错误提示。
*   如果响应体中 `code` 不为 200，应根据 `message` 字段提供详细错误信息。

#### 5.5 数据字典使用

*   将附录中的品类字典表和行政区划字典表加载到内存（例如使用 `map` 或 `slice`），方便查询和验证。
*   在构造请求时，根据需要查表获取正确的编码。

### 6. API 接口列表及关键参数

| 接口名称                                   | 接口地址 (测试环境)                                                      | 请求方法 | 主要Body参数（除通用字段外）                                                                                                                                                                                   | 关键附件                               |
| :----------------------------------------- | :----------------------------------------------------------------------- | :------: | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------- |
| 市场监测日度商超价格数据信息               | `http://58.48.136.183:19090/hbyjbg-api/model/v2/mmsPriceRptTest`         |   POST   | `loginName`, `commodityId`, `price`, `rptdate`                                                                                                                                                                 | 市场监测品类字典表                     |
| 市场监测日度批发价格数据信息               | `http://58.48.136.183:19090/hbyjbg-api/model/v2/mmsPriceRptTest`         |   POST   | `loginName`, `commodityId`, `price` (批发价格), `rptdate`                                                                                                                                                      | 市场监测品类字典表                     |
| 市场监测日度农贸价格数据信息               | `http://58.48.136.183:19090/hbyjbg-api/model/v2/mmsPriceRptTest`         |   POST   | `loginName`, `commodityId`, `price` (零售价格), `rptdate`                                                                                                                                                      | 市场监测品类字典表                     |
| 应急保供日度商超进销存数据信息             | `http://58.48.136.183:19090/hbyjbg-api/model/v2/ecdbStoreDayRptTest`     |   POST   | `loginName`, `commodityId`, `stock`, `amount`, `instock`, `rptdate`, `originPlace`                                                                                                                             | 应急保供零售品类字典表, 行政区划字典表 |
| 应急保供日度批发进销存数据信息             | `http://58.48.136.183:19090/hbyjbg-api/model/v2/wisDayJxcRptTest`        |   POST   | `loginName`, `commodityId`, `stock`, `amount`, `instock`, `rptdate`, `originPlace`                                                                                                                             | 应急保供批发品类字典表, 行政区划字典表 |
| 食品加工企业月度数据信息                   | `http://58.48.136.183:19090/hbyjbg-api/model/v2/ecdbProductMonthRptTest` |   POST   | `loginName`, `rptdate`, `commodityId`, `totalStore`, `totalProduction`, `totalSales`, `dayProduction`, `takeOutAmount`                                                                                         | 食品加工品类字典表                     |
| 物流运输信息监测数据                       | `http://58.48.136.183:19090/hbyjbg-api/model/v2/transportRptTest`        |   POST   | `deployId`, `loginName`, `enterName`, `vehicle`, `driver`, `driverMobile`, `departProvince/City/County/Place/Time`, `arrivalProvince/City/County/Place/Time`, `isArrival`, `isFreeze`, `commodityId`, `number` | 物流数据品类字典表, 行政区划字典表     |
| 终端配送信息监测数据                       | `http://58.48.136.183:19090/hbyjbg-api/model/v2/deliverRptTest`          |   POST   | `deployId`, `loginName`, `enterName`, `waybill`, `courier`, `courierMobile`, `departDepot`, `departTime`, `arrivalAreaName`, `arrivalTime`, `isArrival`, `commodityId`                                         | 物流数据品类字典表                     |
| （网点维度）应急保供日度商超进销存数据信息 | `http://58.48.136.183:1990/proxy/sgp/subject/uploadProductDtl`           |   POST   | `nodeCode` (或 `foreignId`), `commodityId`, `stock` (可选), `instock` (可选), `amount` (可选), `rpdate` (yyy-MM-dd hh:mm:ss)                                                                                   | 应急保供零售品类字典表                 |
| （网点维度）应急保供日度批发进销存数据信息 | `http://58.48.136.183:1990/proxy/sgp/subject/wisUploadProductDtl`        |   POST   | `nodeCode` (或 `foreignId`), `commodityId`, `stock` (可选), `instock` (可选), `amount` (可选), `rpdate` (yyy-MM-dd hh:mm:ss)                                                                                   | 应急保供批发品类字典表                 |

### 7. 附录使用

请务必查阅/下载附录中的相关数据字典（品类字典表、行政区划字典表等），并在调用接口前，确保传递的参数值符合字典定义，特别是 `commodityId`, `originPlace`, `departProvince` 等编码类参数。

---

**Golang 开发注意事项和建议:**

1.  **HTTP 客户端:**
    *   可以使用 `net/http` 包来构建和发送请求。
    *   Consider using a custom HTTP client with a `Timeout` to prevent requests from hanging indefinitely.
    *   对于需要重试的场景，可以考虑使用像 `retryablehttp` 这样的库。

2.  **JSON 编解码:**
    *   使用 `encoding/json` 包进行 JSON 数据的序列化 (`json.Marshal`) 和反序列化 (`json.Unmarshal`)。
    *   为请求体和响应体定义清晰的 Go 结构体，并使用 `json:"..."` 标签来映射 JSON 字段名。

3.  **签名生成:**
    *   实现一个独立的 Go 包来处理认证签名逻辑，该包可以包含一个 `generateSign` 函数。
    *   如果文档提供具体的生成算法（如MD5, SHA256等），请确保实现正确。例如，如果签名是`secretId` + `timestamp` + `secretKey` 的MD5值，则需要按照这个逻辑实现。

4.  **数据模型:**
    *   为每个 API 的请求体和响应体定义对应的 Go 结构体。
    *   对于 `items` 数组中的对象，也要有单独的结构体。

5.  **错误处理和日志:**
    *   为所有可能的 API 错误码（如 400, 401, 403, 500）实现Graceful handling 和清晰的错误信息返回。
    *   集成日志库（如 `log` 包或更高级的 `logrus`, `zap` 等）来记录请求、响应和错误信息，方便调试。

6.  **配置管理:**
    *   将 `secretId`, `secretKey`, 接口基础地址等配置信息外部化，可以使用常量、环境变量或配置文件。

7.  **复用性:**
    *   为签名生成、HTTP请求发送、JSON编解码等通用功能创建可复用的函数或结构体。

