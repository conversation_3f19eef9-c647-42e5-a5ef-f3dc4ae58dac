package ui

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/widget"
)

// fixedWidthEntry 是一个自定义控件，它具有固定的最小宽度
type fixedWidthEntry struct {
	widget.Entry
	minWidth float32
}

// newFixedWidthEntry 创建一个新的自定义 Entry
// 你需要调用这个函数来创建控件，而不是 widget.NewEntry()
func newFixedWidthEntry(minWidth float32) *fixedWidthEntry {
	e := &fixedWidthEntry{minWidth: minWidth}
	// ExtendBaseWidget 是必须的，它用来完成控件的初始化
	e.ExtendBaseWidget(e)
	return e
}

// MinSize 是关键！我们重写了这个方法来返回我们想要的尺寸。
func (e *fixedWidthEntry) MinSize() fyne.Size {
	originalMinSize := e.Entry.MinSize()
	// 高度我们保持不变，使用原始的最小高度
	// 宽度我们使用自定义的最小宽度
	return fyne.NewSize(e.minWidth, originalMinSize.Height)
}
