package logger

import (
	"io"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

var Log *logrus.Logger

func Init() {
	Log = logrus.New()

	logDir := "logs"
	if err := os.<PERSON>dir<PERSON>ll(logDir, os.ModePerm); err != nil {
		Log.Fatalf("创建日志目录失败: %v", err)
	}

	logFile := &lumberjack.Logger{
		Filename:   filepath.Join(logDir, "app.log"),
		MaxSize:    10, // megabytes
		MaxBackups: 3,
		MaxAge:     28, //days
		Compress:   true,
	}

	mw := io.MultiWriter(os.Stdout, logFile)

	Log.SetOutput(mw)
	Log.SetReportCaller(true)
	Log.SetFormatter(&logrus.JSONFormatter{})
	Log.SetLevel(logrus.InfoLevel)

	Log.Info("日志系统初始化成功")
}