package ui

import (
	"fmt"
	"strconv"

	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/models"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

func (a *App) createHistoryTab() fyne.CanvasObject {
	//创建一个临时可计算高度的label
	tempLabel := widget.NewLabel("")
	tempLabel.Wrapping = fyne.TextWrapWord
	var loadSubmissions func()
	var submissions []models.Submission
	currentPage := 1
	var totalPages int
	// 创建表格
	table := widget.NewTable(
		func() (int, int) {
			// 返回行数和列数
			return len(submissions) + 1, 8 // +1 for header, 8 columns
		},
		func() fyne.CanvasObject {
			label := widget.NewLabel("")
			label.Wrapping = fyne.TextWrapWord
			return container.NewStack(label)
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			containerCell := obj.(*fyne.Container)
			label := containerCell.Objects[0].(*widget.Label)
			// 确保标签始终启用文本换行
			label.Wrapping = fyne.TextWrapWord

			if id.Row == 0 {
				// Header row
				headers := []string{"序号", "用户", "上报类型", "文件路径", "开始时间", "结束时间", "状态", "错误信息"}
				if id.Col < len(headers) {
					label.SetText(headers[id.Col])
				}
				return
			}

			// Data rows
			if submissions == nil || id.Row-1 >= len(submissions) {
				label.SetText("")
				return
			}

			submission := submissions[id.Row-1]
			switch id.Col {
			case 0:
				label.SetText(strconv.Itoa(id.Row))
			case 1:
				label.SetText(submission.User.Username)
			case 2:
				label.SetText(submission.ReportType)
			case 3:
				label.SetText(submission.FilePath)
			case 4:
				label.SetText(submission.StartTime.Format("2006-01-02 15:04:05"))
			case 5:
				label.SetText(submission.EndTime.Format("2006-01-02 15:04:05"))
			case 6:
				if submission.Status == "success" {
					label.SetText("成功")
				} else {
					label.SetText("失败")
				}
			case 7:
				label.SetText(submission.ErrorMsg)
			}
		},
	)

	// 设置列宽
	table.SetColumnWidth(0, 60)                     // 序号
	table.SetColumnWidth(1, 100)                    // 用户
	table.SetColumnWidth(2, 150)                    // 上报类型
	table.SetColumnWidth(3, 200)                    // 文件路径
	table.SetColumnWidth(4, 150)                    // 开始时间
	table.SetColumnWidth(5, 150)                    // 结束时间
	table.SetColumnWidth(6, 80)                     // 状态
	table.SetColumnWidth(7, ERROR_MSG_COLUMN_WIDTH) // 错误信息

	// 设置行高
	for i := 0; i < PageSize+1; i++ {
		table.SetRowHeight(i, DEFAULT_ROW_HEIGHT)
	}

	loadSubmissions = func() {
		count, _ := database.GetSubmissionsCount()
		pageCount := (count + PageSize - 1) / PageSize
		if pageCount == 0 {
			pageCount = 1
		}
		totalPages = int(pageCount)

		subs, err := database.GetSubmissions(currentPage, PageSize)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		submissions = subs

		for i, sub := range submissions {
			rowID := i + 1

			// 将文本放入临时 Label
			tempLabel.SetText(sub.ErrorMsg)
			// 确保临时标签启用文本换行
			tempLabel.Wrapping = fyne.TextWrapWord

			// 使用一个临时容器来强制宽度，以便正确计算换行后的高度
			tempContainer := container.NewStack(tempLabel)
			tempContainer.Resize(fyne.NewSize(ERROR_MSG_COLUMN_WIDTH, 0))

			// 获取在该宽度下所需的最小高度
			requiredHeight := tempLabel.MinSize().Height

			// 确保行高不低于我们的默认值，并为长文本添加额外空间
			if requiredHeight < DEFAULT_ROW_HEIGHT {
				requiredHeight = DEFAULT_ROW_HEIGHT
			} else {
				// 为长文本添加一些额外的空间，避免文本被截断
				requiredHeight += 10
			}

			// 应用计算出的高度
			table.SetRowHeight(rowID, requiredHeight)
		}

		// 更新分页标签
		// pageLabel.SetText(fmt.Sprintf("第 %d 页，共 %d 页", currentPage, totalPages))
		table.Refresh()
	}

	// 创建分页控件
	pageLabel := widget.NewLabel(fmt.Sprintf("第 %d 页，共 %d 页", currentPage, totalPages))
	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadSubmissions() // 加载新页面的数据
		}
	})
	nextButton := widget.NewButton("下一页", func() {
		if currentPage < int(totalPages) {
			currentPage++
			loadSubmissions() // 加载新页面的数据
		}
	})

	paginationContainer := container.NewHBox(
		prevButton,
		pageLabel,
		nextButton,
	)
	loadSubmissions()
	return container.NewBorder(
		nil,
		container.NewCenter(paginationContainer),
		nil,
		nil,
		table,
	)
}
