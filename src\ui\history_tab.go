package ui

import (
	"fmt"
	"strconv"

	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/models"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

func (a *App) createHistoryTab() fyne.CanvasObject {
	var loadSubmissions func()
	var submissions []models.Submission
	currentPage := 1
	totalCount := int64(0)
	totalPages := 0

	// 创建表格
	table := widget.NewTable(
		func() (int, int) {
			rowCount := len(submissions) + 1 // +1 for header
			if rowCount < PageSize+1 {
				rowCount = PageSize + 1
			}
			return rowCount, 8 // 8 columns
		},
		func() fyne.CanvasObject {
			// label := widget.NewLabel("")
			// label.Wrapping = fyne.TextWrapWord
			return container.NewStack(NewHoverableLabel(a.mainWin.Canvas()))
		},
		func(id widget.TableCellID, obj fyne.CanvasObject) {
			containerCell := obj.(*fyne.Container)
			cellLabel := containerCell.Objects[0].(*HoverableLabel)

			if id.Row == 0 {
				headers := []string{"序号", "用户", "上报类型", "文件路径", "开始时间", "结束时间", "状态", "错误信息"}
				if id.Col < len(headers) {
					cellLabel.SetText(headers[id.Col])
				}
				return
			}

			if submissions == nil || id.Row-1 >= len(submissions) {
				cellLabel.SetText("")
				return
			}

			submission := submissions[id.Row-1]
			var text string
			switch id.Col {
			case 0:
				text = strconv.Itoa(id.Row)
			case 1:
				text = submission.User.Username
			case 2:
				text = submission.ReportType
			case 3:
				text = submission.FilePath
			case 4:
				text = submission.StartTime.Format("2006-01-02 15:04:05")
			case 5:
				text = submission.EndTime.Format("2006-01-02 15:04:05")
			case 6:
				if submission.Status == "success" {
					text = "成功"
				} else {
					text = "失败"
				}
			case 7:
				text = submission.ErrorMsg
			}
			cellLabel.SetText(text) // 使用 SetText 设置完整文本
		},
	)

	// 设置列宽
	table.SetColumnWidth(0, 60)                     // 序号
	table.SetColumnWidth(1, 100)                    // 用户
	table.SetColumnWidth(2, 150)                    // 上报类型
	table.SetColumnWidth(3, 200)                    // 文件路径
	table.SetColumnWidth(4, 150)                    // 开始时间
	table.SetColumnWidth(5, 150)                    // 结束时间
	table.SetColumnWidth(6, 80)                     // 状态
	table.SetColumnWidth(7, ERROR_MSG_COLUMN_WIDTH) // 错误信息

	// 设置行高
	table.SetRowHeight(0, DEFAULT_ROW_HEIGHT) // 表头行高

	// 创建分页标签
	pageLabel := widget.NewLabel(fmt.Sprintf("第 %d 页，共 %d 条记录", currentPage, totalCount))

	// 更新分页标签函数
	updatePageLabel := func() {
		pageCount := (totalCount + PageSize - 1) / PageSize
		if pageCount == 0 {
			pageCount = 1
		}
		totalPages = int(pageCount)
		pageLabel.SetText(fmt.Sprintf("第 %d 页，共 %d 条记录，总共 %d 页", currentPage, totalCount, totalPages))
	}

	// 加载数据函数
	loadSubmissions = func() {
		// 获取总记录数
		var err error
		totalCount, err = database.GetSubmissionsCount()
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}

		// 更新分页信息
		updatePageLabel()

		// 获取当前页数据
		subs, err := database.GetSubmissions(currentPage, PageSize)
		if err != nil {
			dialog.ShowError(err, a.mainWin)
			return
		}
		submissions = subs
		// 刷新表格
		table.Refresh()
	}

	// 创建刷新按钮
	refreshButton := widget.NewButton("刷新", func() {
		loadSubmissions()
	})

	// 创建分页控件
	prevButton := widget.NewButton("上一页", func() {
		if currentPage > 1 {
			currentPage--
			loadSubmissions()
		}
	})
	nextButton := widget.NewButton("下一页", func() {
		if currentPage < totalPages {
			currentPage++
			loadSubmissions()
		}
	})

	paginationContainer := container.NewHBox(
		prevButton,
		pageLabel,
		nextButton,
		refreshButton, // 添加刷新按钮到分页控件
	)

	// 初始加载数据
	loadSubmissions()
	a.refreshTabs["上报历史"] = loadSubmissions
	// 返回包含刷新功能的容器
	return container.NewBorder(
		nil,
		container.NewCenter(paginationContainer),
		nil,
		nil,
		table,
	)
}
