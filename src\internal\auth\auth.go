package auth

import (
	"database/sql"
	"fmt"

	"simple_inventory_management_system/api"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"simple_inventory_management_system/internal/models"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
)

// 查询用户信息
func getUserByUsername(username string) (*api.User, string, error) {
	var returnUser *models.User

	err := database.DB.Raw("SELECT id, password_hash, role_id FROM users WHERE username = ?", username).Scan(&returnUser).Error
	if err == sql.ErrNoRows {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("用户不存在")
		return nil, "", fmt.Errorf("用户不存在")
	} else if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("查询数据库失败")
		return nil, "", fmt.<PERSON>rrorf("数据库查询失败")
	}
	return &api.User{ID: int(returnUser.ID), Username: username, RoleID: int(returnUser.RoleID)}, returnUser.PasswordHash, nil
}

// 查询用户角色
func getUserRole(roleId int) (string, error) {
	// var roleName string
	return database.GetUserRole(roleId)
	// err := database.DB.Raw("SELECT name FROM roles WHERE id =?", roleId).Scan(&roleName)
	// if err != nil {
	// 	logger.Log.WithFields(logrus.Fields{
	// 		"error": err,
	// 	}).Error("无法获取用户角色")
	// 	return "", fmt.Errorf("无法获取用户角色!")
	// }
	// return roleName, nil
}

// 验证密码
func verifyPassword(hashedPassword, password string) error {
	if err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password)); err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("密码错误")
		return fmt.Errorf("密码错误")
	}
	return nil
}

// AuthenticateUser verifies user credentials and returns the user on success.
func AuthenticateUser(username, password string) (*api.User, error) {
	user, hashedPassword, err := getUserByUsername(username)
	if err != nil {
		return nil, err
	}

	if err = verifyPassword(hashedPassword, password); err != nil {
		return nil, err
	}

	roleName, err := getUserRole(user.RoleID)
	if err != nil {
		return nil, err
	}

	user.Role = roleName
	return user, nil
}

func GetUsers(username string, pageSize, pageNum int) ([]api.User, int, error) {
	// 查询总数
	var count int
	query := "SELECT count(*) FROM users"
	args := []interface{}{}

	if username != "" {
		query += " WHERE username LIKE ?"
		args = append(args, "%"+username+"%")
	}

	err := database.DB.Raw(query, args...).Scan(&count).Error
	if err != nil {
		return nil, 0, err
	}

	// 查询用户列表
	query = "SELECT u.id, u.username, u.role_id, r.name FROM users u LEFT JOIN roles r ON u.role_id = r.id"
	args = []interface{}{}

	if username != "" {
		query += " WHERE u.username LIKE ?"
		args = append(args, "%"+username+"%")
	}

	query += " LIMIT ? OFFSET ?"
	args = append(args, pageSize, (pageNum-1)*pageSize)

	rows, err := database.DB.Raw(query, args...).Rows()
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("查询用户失败")
		return nil, 0, fmt.Errorf("查询用户失败")
	}
	defer rows.Close()

	var users []api.User
	for rows.Next() {
		var user api.User
		var roleName string
		if err := rows.Scan(&user.ID, &user.Username, &user.RoleID, &roleName); err != nil {
			logger.Log.WithFields(logrus.Fields{
				"error": err,
			}).Error("扫描用户数据失败")
			return nil, 0, fmt.Errorf("扫描用户数据失败")
		}
		user.Role = roleName
		users = append(users, user)
	}

	return users, count, nil
}

// AddUser 添加用户
func AddUser(username, password, roleName string) error {
	// 查询角色ID
	var roleID int
	err := database.DB.Raw("SELECT id FROM roles WHERE name = ?", roleName).Scan(&roleID).Error
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("查询角色失败")
		return fmt.Errorf("查询角色失败!")
	}

	// 生成密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("生成密码哈希失败")
		return fmt.Errorf("生成密码哈希失败")
	}

	// 插入用户
	database.AddUser(username, string(hashedPassword), roleName).Error()
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("添加用户失败")
		return fmt.Errorf("添加用户失败!")
	}

	return nil
}

// DeleteUser 删除用户
func DeleteUser(userID int) error {
	err := database.DeleteUser(userID).Error
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Error("删除用户失败")
		return fmt.Errorf("删除用户失败!")
	}
	return nil
}
