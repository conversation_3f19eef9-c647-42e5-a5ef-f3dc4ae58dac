package ui

import (
	"simple_inventory_management_system/api"
	"simple_inventory_management_system/internal/auth"
	"simple_inventory_management_system/internal/logger"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/widget"

	"fmt"

	"github.com/sirupsen/logrus"
)

type App struct {
	app         fyne.App
	window      fyne.Window
	currentUser *api.User
}

// const PageSize = 10
const (
	PageSize                       = 20
	ERROR_MSG_COLUMN_WIDTH float32 = 500
	DEFAULT_ROW_HEIGHT     float32 = 28
)

func NewApp() *App {
	a := app.New()

	// 设置自定义主题
	fontPath := EnsureMicrosoftYaheiFont()
	a.Settings().SetTheme(NewMyTheme(fontPath))
	companyName, err := getConfig("companyName")
	if err != nil {
		companyName = ""
	}
	title := fmt.Sprintf("%s - 生活必需品流通保供监测", companyName)
	w := a.New<PERSON>indow(title)

	return &App{
		app:    a,
		window: w,
	}
}

func (a *App) Run() {
	a.showLoginScreen()
	a.mainWin.Resize(fyne.NewSize(400, 200))
	a.mainWin.CenterOnScreen() // 关键居中调用
	a.mainWin.ShowAndRun()
}

func (a *App) handleLogin(username, password string) error {
	logger.Log.WithFields(logrus.Fields{
		"username": username,
		"password": "********",
	}).Info("用户尝试登录")

	user, err := auth.AuthenticateUser(username, password)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"username": username,
			"error":    err,
		}).Error("用户登录失败")
		return err
	}
	a.currentUser = user
	logger.Log.WithFields(logrus.Fields{
		"username": username,
		"role":     user.Role,
	}).Info("用户登录成功")
	a.showMainScreen()
	return nil
}

func (a *App) showLoginScreen() {
	usernameEntry := widget.NewEntry()
	usernameEntry.SetPlaceHolder("用户名")
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("密码")

	loginButton := widget.NewButton("登录", func() {
		if err := a.handleLogin(usernameEntry.Text, passwordEntry.Text); err != nil {
			dialog.ShowError(err, a.mainWin)
		}
	})

	content := container.NewVBox(
		widget.NewForm(
			widget.NewFormItem("用户名", usernameEntry),
			widget.NewFormItem("密码", passwordEntry),
		),
		container.NewCenter(loginButton),
	)

	setupReturnKeySubmit(a.mainWin, usernameEntry, passwordEntry, loginButton)

	a.mainWin.SetContent(content)
}

func setupReturnKeySubmit(win fyne.Window, username, password *widget.Entry, loginBtn *widget.Button) {
	// 为用户名和密码输入框分别设置回车键监听
	username.OnSubmitted = func(text string) {
		// 当在用户名字段按回车，焦点移到密码字段
		win.Canvas().Focus(password)
	}

	password.OnSubmitted = func(text string) {
		// 当在密码字段按回车，触发登录
		loginBtn.OnTapped()
	}

	// 设置全局回车键监听（备用）
	if deskCanvas, ok := win.Canvas().(desktop.Canvas); ok {
		deskCanvas.SetOnKeyDown(func(event *fyne.KeyEvent) {
			if event.Name == fyne.KeyReturn || event.Name == fyne.KeyEnter {
				// 检查当前焦点在哪个字段
				focused := win.Canvas().Focused()
				if focused == username || focused == password {
					loginBtn.OnTapped()
				}
			}
		})
	}
}

func (a *App) showMainScreen() {
	a.safeResizeAndCenter(1024, 768)
	tabs := container.NewAppTabs()

	tabs.Append(container.NewTabItem("数据上报", a.createSubmissionTab()))
	tabs.Append(container.NewTabItem("上报历史", a.createHistoryTab()))

	if a.currentUser.Role == "管理员" {
		tabs.Append(container.NewTabItem("系统管理", a.createSystemManagementTab()))
		tabs.Append(container.NewTabItem("行政区划", a.createAdminDivisionsTab()))
		tabs.Append(container.NewTabItem("地区代码", a.createRegionsTab()))
		tabs.Append(container.NewTabItem("品类管理", a.createCategoriesTab()))
	}

	a.mainWin.SetContent(tabs)
}

func (a *App) safeResizeAndCenter(width, height float32) {
	// 立即刷新状态
	a.mainWin.Canvas().Refresh(a.mainWin.Content())

	// 延迟调整以保证UI响应
	go func() {
		a.mainWin.Resize(fyne.NewSize(width, height))
		// a.mainWin.SetFullScreen(true)
		a.mainWin.CenterOnScreen()
	}()
}
