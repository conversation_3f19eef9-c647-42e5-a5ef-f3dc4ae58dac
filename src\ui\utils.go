package ui

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/widget"
	"github.com/flopp/go-findfont"
)

// 确保中文字体文件存在
func EnsureMicrosoftYaheiFont() string {
	fontPaths := findfont.List()
	for _, path := range fontPaths {
		if strings.Contains(strings.ToLower(path), "微软雅黑") {
			return path // 找到了就直接返回
		}
	}
	fmt.Println("Warning: Microsoft Yahei font not found from os.")

	// 如果在系统路径下找不到，则尝试从 "./fonts/msyh.ttf" 加载
	fontPath := "fonts/msyh.ttf"
	if _, err := os.Stat(fontPath); err == nil {
		return fontPath
	}

	// 如果没找到，可以考虑打印一个警告或者从特定路径加载
	fmt.Println("Warning: Microsoft Yahei font not found. UI may not render Chinese characters correctly.")

	return ""
}

func CopyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destinationFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destinationFile.Close()

	_, err = io.Copy(destinationFile, sourceFile)
	return err
}

// newCopyableLabel 创建一个可复制的标签
func newCopyableLabel(text string) fyne.CanvasObject {
	entry := widget.NewEntry()
	entry.SetText(text)
	entry.OnChanged = func(string) {} // 防止文本变化
	// 设置样式使其看起来像标签
	entry.TextStyle = fyne.TextStyle{}
	entry.Theme()
	entry.ExtendBaseWidget(entry) // 确保可扩展
	return entry
}

func enhancedTrim(s string) string {
	return strings.Trim(s, " \t\n\r\"'")
}

// Sha256Hex 计算字符串的SHA-256哈希值并返回十六进制字符串
func Sha256Hex(input string) string {
	hash := sha256.Sum256([]byte(input))
	return fmt.Sprintf("%x", hash[:])
}

// Hmac256 使用HMAC-SHA256算法计算签名
func Hmac256(key, data []byte) []byte {
	h := hmac.New(sha256.New, key)
	h.Write(data)
	return h.Sum(nil)
}

// GenerateSignSHA256 使用SHA-256和HMAC-SHA256生成签名
// 对应Java中的第一个generateSign方法
func GenerateSignSHA256(secretKey, timestamp string) string {
	// 对时间戳进行SHA-256哈希
	content := Sha256Hex(timestamp)

	// 使用HMAC-SHA256计算签名并进行Base64编码
	sign := base64.StdEncoding.EncodeToString(Hmac256([]byte(secretKey), []byte(content)))
	return sign
}

// GenerateSignWithDataSHA256 使用SHA-256和HMAC-SHA256生成带数据的签名
// 对应Java中的第二个generateSign方法
func GenerateSignWithDataSHA256(secretKey, timestamp, datastr string) string {
	// 对时间戳和数据字符串进行SHA-256哈希
	content := Sha256Hex(timestamp + datastr)

	// 使用HMAC-SHA256计算签名并进行Base64编码
	sign := base64.StdEncoding.EncodeToString(Hmac256([]byte(secretKey), []byte(content)))
	return sign
}

func GetDownloadDir() (string, error) {
	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", err
	}

	// 处理不同操作系统的下载目录
	switch runtime.GOOS {
	case "windows":
		// 检查中文系统
		chinesePath := filepath.Join(homeDir, "下载")
		if _, err := os.Stat(chinesePath); err == nil {
			return chinesePath, nil
		}
		return filepath.Join(homeDir, "Downloads"), nil

	case "darwin": // macOS
		return filepath.Join(homeDir, "Downloads"), nil

	case "linux":
		// 优先使用 XDG 规范
		if dir := os.Getenv("XDG_DOWNLOAD_DIR"); dir != "" {
			return dir, nil
		}
		// 检查可能的目录
		possibleDirs := []string{
			filepath.Join(homeDir, "Downloads"),
			filepath.Join(homeDir, "下载"), // 中文系统
			homeDir,
		}
		for _, dir := range possibleDirs {
			if _, err := os.Stat(dir); err == nil {
				return dir, nil
			}
		}
		return homeDir, nil

	default:
		return homeDir, nil
	}
}
