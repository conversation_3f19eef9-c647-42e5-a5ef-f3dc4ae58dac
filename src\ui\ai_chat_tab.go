package ui

import (
	"fmt"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// createAIChatTab 创建AI对话标签页
func (a *App) createAIChatTab() fyne.CanvasObject {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		dialog.ShowError(fmt.Errorf("无法获取当前目录: %v", err), a.mainWin)
		return widget.NewLabel("加载失败")
	}

	// 构建HTML文件的绝对路径
	htmlPath := filepath.Join(currentDir, "web_ui", "index.html")

	// 检查HTML文件是否存在
	if _, err := os.Stat(htmlPath); os.IsNotExist(err) {
		return container.NewCenter(
			container.NewVBox(
				widget.NewLabel("AI对话功能暂不可用"),
				widget.NewLabel(fmt.Sprintf("找不到文件: %s", htmlPath)),
				widget.NewButton("刷新", func() {
					// 重新创建tab内容
					a.refreshAIChatTab()
				}),
			),
		)
	}

	// 创建WebView组件
	webView := NewWebView()

	// 设置窗口引用（用于显示复制通知）
	webView.SetWindow(a.mainWin)

	// 设置HTML文件URL
	fileURL := fmt.Sprintf("file:///%s", filepath.ToSlash(htmlPath))
	webView.LoadURL(fileURL)

	// 创建工具栏
	toolbar := container.NewHBox(
		widget.NewButton("刷新", func() {
			webView.Reload()
		}),
		widget.NewButton("重置", func() {
			// 重新加载页面
			webView.LoadURL(fileURL)
		}),
	)

	// 使用Border布局，工具栏在顶部，WebView在中心
	return container.NewBorder(
		toolbar, // 顶部：工具栏
		nil,     // 底部：无
		nil,     // 左侧：无
		nil,     // 右侧：无
		webView, // 中心：WebView组件
	)
}

// refreshAIChatTab 刷新AI对话标签页
func (a *App) refreshAIChatTab() {
	// 这个方法可以用于刷新标签页内容
	// 在实际应用中，可以重新创建WebView或重新加载内容
}
