package models

import (
	"time"
)

// Role 角色表
type Role struct {
	ID    uint   `gorm:"primaryKey"`
	Name  string `gorm:"unique;not null"`
	Users []User
}

// User 用户表
type User struct {
	ID           uint      `gorm:"primaryKey"`
	Username     string    `gorm:"unique;not null"`
	PasswordHash string    `gorm:"not null"`
	RoleID       uint      `gorm:"not null"`
	Role         Role      `gorm:"foreignKey:RoleID"`
	CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// Config 配置表
type Config struct {
	Key       string    `gorm:"primaryKey"`
	Value     string    `gorm:"not null"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// Category 品类配置表
type Category struct {
	ID           uint   `gorm:"primaryKey"`
	CategoryCode string `gorm:"not null"`
	CategoryName string `gorm:"not null"`
	CategoryType string
	ParentCode   string
	IndustryType string
	PriceUnit    string
	QuantityUnit string
	CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// Region 地区名称代码表
type Region struct {
	ID        uint      `gorm:"primaryKey"`
	Code      string    `gorm:"unique;not null"`
	Name      string    `gorm:"not null"`
	IsDeleted uint      `gorm:"default:0"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// AdminDivision 行政区划字典表
type AdminDivision struct {
	ID        uint      `gorm:"primaryKey"`
	Code      string    `gorm:"unique;not null"`
	UnitName  string    `gorm:"not null"`
	IsDeleted uint      `gorm:"default:0"`
	CreatedAt time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// Submission 提交记录表
type Submission struct {
	ID           uint   `gorm:"primaryKey"`
	UserID       uint   `gorm:"not null"`
	User         User   `gorm:"foreignKey:UserID"`
	ReportType   string `gorm:"not null"`
	FilePath     string `gorm:"not null"`
	ResponseCode int
	ResponseMsg  string
	RequestID    string
	StartTime    time.Time
	EndTime      time.Time
	ResultTime   time.Time
	Status       string
	ErrorMsg     string
	CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}

// ReportCategoryAssociation 上报类型与品类关联表
type ReportCategoryAssociation struct {
	ID           uint      `gorm:"primaryKey"`
	ReportType   string    `gorm:"not null"`
	CategoryCode string    `gorm:"not null"`
	CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP"`
}
