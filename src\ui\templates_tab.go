package ui

import (
	"fmt"
	"os"
	"path/filepath"

	"simple_inventory_management_system/internal/logger"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/layout"
	"fyne.io/fyne/v2/widget"

	"github.com/sirupsen/logrus"
)

// createTemplatesTab 创建模板下载标签页
func (a *App) createTemplatesTab() fyne.CanvasObject {
	// 创建标题标签
	titleLabel := widget.NewLabelWithStyle("模板下载", fyne.TextAlignCenter, fyne.TextStyle{Bold: true})

	// 创建价格日报模板下载按钮
	priceTemplateButton := widget.NewButton("价格日报模板", func() {
		// 源文件路径
		srcPath := filepath.Join("doc", "价格日报模板.xlsx")
		
		// 检查源文件是否存在
		if _, err := os.Stat(srcPath); os.IsNotExist(err) {
			dialog.ShowError(fmt.Errorf("模板文件不存在: %s", srcPath), a.mainWin)
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"path":  srcPath,
				"user":  a.user.Username,
			}).Error("模板文件不存在")
			return
		}

		// 创建文件保存对话框并设置默认文件名
		saveDialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
			if err != nil || writer == nil {
				return
			}
			
			// 关闭写入器
			defer writer.Close()
			
			// 复制文件
			err = CopyFile(srcPath, writer.URI().Path())
			if err != nil {
				dialog.ShowError(fmt.Errorf("保存模板文件失败: %v", err), a.mainWin)
				logger.Log.WithFields(logrus.Fields{
					"error":    err,
					"srcPath":  srcPath,
					"destPath": writer.URI().Path(),
					"user":     a.user.Username,
				}).Error("保存模板文件失败")
				return
			}
			
			dialog.ShowInformation("下载成功", "价格日报模板已成功下载", a.mainWin)
		}, a.mainWin)
		
		// 设置默认文件名
		saveDialog.SetFileName("价格日报模板.xlsx")
		saveDialog.Show()
	})

	// 创建库存日报模板下载按钮
	inventoryTemplateButton := widget.NewButton("库存日报模板", func() {
		// 源文件路径
		srcPath := filepath.Join("doc", "库存日报模板.xlsx")
		
		// 检查源文件是否存在
		if _, err := os.Stat(srcPath); os.IsNotExist(err) {
			dialog.ShowError(fmt.Errorf("模板文件不存在: %s", srcPath), a.mainWin)
			logger.Log.WithFields(logrus.Fields{
				"error": err,
				"path":  srcPath,
				"user":  a.user.Username,
			}).Error("模板文件不存在")
			return
		}

		// 创建文件保存对话框并设置默认文件名
		saveDialog := dialog.NewFileSave(func(writer fyne.URIWriteCloser, err error) {
			if err != nil || writer == nil {
				return
			}
			
			// 关闭写入器
			defer writer.Close()
			
			// 复制文件
			err = CopyFile(srcPath, writer.URI().Path())
			if err != nil {
				dialog.ShowError(fmt.Errorf("保存模板文件失败: %v", err), a.mainWin)
				logger.Log.WithFields(logrus.Fields{
					"error":    err,
					"srcPath":  srcPath,
					"destPath": writer.URI().Path(),
					"user":     a.user.Username,
				}).Error("保存模板文件失败")
				return
			}
			
			dialog.ShowInformation("下载成功", "库存日报模板已成功下载", a.mainWin)
		}, a.mainWin)
		
		// 设置默认文件名
		saveDialog.SetFileName("库存日报模板.xlsx")
		saveDialog.Show()
	})

	// 创建按钮容器，使用VBox垂直排列
	buttonsContainer := container.NewVBox(
		container.NewHBox(layout.NewSpacer(), priceTemplateButton, layout.NewSpacer()),
		container.NewHBox(layout.NewSpacer(), inventoryTemplateButton, layout.NewSpacer()),
	)

	// 创建说明标签
	descriptionLabel := widget.NewLabel("点击按钮下载对应的模板文件")
	descriptionLabel.Alignment = fyne.TextAlignCenter

	// 将所有元素放入垂直容器中
	content := container.NewVBox(
		titleLabel,
		container.NewPadded(buttonsContainer),
		descriptionLabel,
	)

	// 使用Center容器使内容居中显示
	return container.NewCenter(content)
}